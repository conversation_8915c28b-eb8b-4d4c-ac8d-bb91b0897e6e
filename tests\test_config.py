"""
配置管理测试
"""

import pytest
import tempfile
import yaml
from pathlib import Path
import sys

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.utils.config import ConfigManager, DatasetConfig, ModelConfig, TrainingConfig


class TestConfigManager:
    """测试配置管理器"""
    
    def test_load_yaml(self):
        """测试YAML文件加载"""
        # 创建临时YAML文件
        test_data = {"name": "test", "value": 123}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_data, f)
            temp_path = f.name
        
        config_manager = ConfigManager()
        loaded_data = config_manager.load_yaml(temp_path)
        
        assert loaded_data == test_data
        
        # 清理
        Path(temp_path).unlink()
    
    def test_dataset_config(self):
        """测试数据集配置"""
        config = DatasetConfig(
            name="test_dataset",
            path="/path/to/dataset",
            config_file="dataset.yaml",
            num_classes=5,
            image_size=640
        )
        
        assert config.name == "test_dataset"
        assert config.path == "/path/to/dataset"
        assert config.num_classes == 5
        assert config.image_size == 640
    
    def test_model_config(self):
        """测试模型配置"""
        config = ModelConfig(
            name="yolov8n",
            version="v8",
            variant="n",
            num_classes=1,
            pretrained=True
        )
        
        assert config.name == "yolov8n"
        assert config.version == "v8"
        assert config.variant == "n"
        assert config.pretrained is True
    
    def test_training_config(self):
        """测试训练配置"""
        config = TrainingConfig(
            epochs=100,
            batch_size=16,
            learning_rate=0.01,
            optimizer="AdamW"
        )
        
        assert config.epochs == 100
        assert config.batch_size == 16
        assert config.learning_rate == 0.01
        assert config.optimizer == "AdamW"