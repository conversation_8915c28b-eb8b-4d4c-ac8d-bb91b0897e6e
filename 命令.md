# 🚀 YOLO多模型训练命令大全

## 📖 快速使用指南

复制对应的命令到终端运行即可开始训练。所有命令都在项目根目录下执行。

---

## 🔥 YOLOv8 模型训练

### YOLOv8n (最小最快)
```bash
# 基础训练
python scripts/train.py --model yolov8n --dataset aqua --experiment yolov8n_aqua_baseline --epochs 100 --batch-size 16 --device 0

# 长期训练
python scripts/train.py --model yolov8n --dataset aqua --experiment yolov8n_aqua_long --epochs 200 --batch-size 16 --learning-rate 0.001 --device 0

# 大批次训练
python scripts/train.py --model yolov8n --dataset aqua --experiment yolov8n_aqua_large_batch --epochs 100 --batch-size 32 --device 0
```

### YOLOv8s (小型)
```bash
# 基础训练
python scripts/train.py --model yolov8s --dataset aqua --experiment yolov8s_aqua_baseline --epochs 100 --batch-size 16 --device 0

# 高精度训练
python scripts/train.py --model yolov8s --dataset aqua --experiment yolov8s_aqua_high_acc --epochs 150 --batch-size 12 --learning-rate 0.0005 --device 0
```

### YOLOv8m (中型)
```bash
# 基础训练
python scripts/train.py --model yolov8m --dataset aqua --experiment yolov8m_aqua_baseline --epochs 100 --batch-size 8 --device 0

# 精调训练
python scripts/train.py --model yolov8m --dataset aqua --experiment yolov8m_aqua_fine_tune --epochs 120 --batch-size 8 --learning-rate 0.0003 --device 0
```

### YOLOv8l (大型)
```bash
# 基础训练
python scripts/train.py --model yolov8l --dataset aqua --experiment yolov8l_aqua_baseline --epochs 100 --batch-size 4 --device 0

# 高质量训练
python scripts/train.py --model yolov8l --dataset aqua --experiment yolov8l_aqua_premium --epochs 150 --batch-size 4 --learning-rate 0.0002 --device 0
```

### YOLOv8x (超大型)
```bash
# 基础训练
python scripts/train.py --model yolov8x --dataset aqua --experiment yolov8x_aqua_baseline --epochs 100 --batch-size 2 --device 0

# 终极精度训练
python scripts/train.py --model yolov8x --dataset aqua --experiment yolov8x_aqua_ultimate --epochs 200 --batch-size 2 --learning-rate 0.0001 --device 0
```

---

## ⚡ YOLOv11 模型训练

### YOLOv11n (最新最小)
```bash
# 基础训练
python scripts/train.py --model yolo11n --dataset aqua --experiment yolo11n_aqua_baseline --epochs 100 --batch-size 16 --device 0

# 快速验证训练
python scripts/train.py --model yolo11n --dataset aqua --experiment yolo11n_aqua_quick --epochs 50 --batch-size 20 --device 0
```

### YOLOv11s (最新小型)
```bash
# 基础训练
python scripts/train.py --model yolo11s --dataset aqua --experiment yolo11s_aqua_baseline --epochs 100 --batch-size 16 --device 0

# 优化训练
python scripts/train.py --model yolo11s --dataset aqua --experiment yolo11s_aqua_optimized --epochs 120 --batch-size 14 --learning-rate 0.0008 --device 0
```

### YOLOv11m (最新中型)
```bash
# 基础训练
python scripts/train.py --model yolo11m --dataset aqua --experiment yolo11m_aqua_baseline --epochs 100 --batch-size 8 --device 0

# 深度训练
python scripts/train.py --model yolo11m --dataset aqua --experiment yolo11m_aqua_deep --epochs 180 --batch-size 6 --learning-rate 0.0004 --device 0
```

### YOLOv11l (最新大型)
```bash
# 基础训练
python scripts/train.py --model yolo11l --dataset aqua --experiment yolo11l_aqua_baseline --epochs 100 --batch-size 4 --device 0

# 专业训练
python scripts/train.py --model yolo11l --dataset aqua --experiment yolo11l_aqua_professional --epochs 160 --batch-size 4 --learning-rate 0.0003 --device 0
```

### YOLOv11x (最新超大)
```bash
# 基础训练
python scripts/train.py --model yolo11x --dataset aqua --experiment yolo11x_aqua_baseline --epochs 100 --batch-size 2 --device 0

# 顶级训练
python scripts/train.py --model yolo11x --dataset aqua --experiment yolo11x_aqua_premium --epochs 200 --batch-size 2 --learning-rate 0.0002 --device 0
```

---

## 🎯 YOLOv13 模型训练

### YOLOv13n (最新nano)
```bash
# 基础训练
python scripts/train.py --model yolov13n --dataset aqua --experiment yolov13n_aqua_baseline --epochs 100 --batch-size 16 --device 0

# 快速训练
python scripts/train.py --model yolov13n --dataset aqua --experiment yolov13n_aqua_fast --epochs 80 --batch-size 18 --learning-rate 0.012 --device 0
```

### YOLOv13s (最新small)
```bash
# 基础训练
python scripts/train.py --model yolov13s --dataset aqua --experiment yolov13s_aqua_baseline --epochs 100 --batch-size 16 --device 0

# 增强训练
python scripts/train.py --model yolov13s --dataset aqua --experiment yolov13s_aqua_enhanced --epochs 130 --batch-size 12 --learning-rate 0.0006 --device 0
```

### YOLOv13m (最新medium)
```bash
# 基础训练
python scripts/train.py --model yolov13m --dataset aqua --experiment yolov13m_aqua_baseline --epochs 100 --batch-size 8 --device 0

# 平衡训练
python scripts/train.py --model yolov13m --dataset aqua --experiment yolov13m_aqua_balanced --epochs 140 --batch-size 8 --learning-rate 0.0005 --device 0
```

### YOLOv13l (最新large)
```bash
# 基础训练
python scripts/train.py --model yolov13l --dataset aqua --experiment yolov13l_aqua_baseline --epochs 100 --batch-size 4 --device 0

# 高性能训练
python scripts/train.py --model yolov13l --dataset aqua --experiment yolov13l_aqua_high_perf --epochs 160 --batch-size 4 --learning-rate 0.0003 --device 0
```

### YOLOv13x (最新xlarge)
```bash
# 基础训练
python scripts/train.py --model yolov13x --dataset aqua --experiment yolov13x_aqua_baseline --epochs 100 --batch-size 2 --device 0

# 最强训练
python scripts/train.py --model yolov13x --dataset aqua --experiment yolov13x_aqua_ultimate --epochs 200 --batch-size 2 --learning-rate 0.0001 --device 0
```

---

## 🔧 特殊训练配置

### CPU训练 (如果没有GPU)
```bash
# 使用CPU训练YOLOv8n
python scripts/train.py --model yolov8n --dataset aqua --experiment yolov8n_aqua_cpu --epochs 50 --batch-size 4 --device cpu

# 使用CPU训练YOLOv11n
python scripts/train.py --model yolo11n --dataset aqua --experiment yolo11n_aqua_cpu --epochs 50 --batch-size 4 --device cpu
```

### 多GPU训练
```bash
# 使用多GPU训练
python scripts/train.py --model yolov8m --dataset aqua --experiment yolov8m_aqua_multi_gpu --epochs 100 --batch-size 16 --device 0,1

# 使用多GPU训练大模型
python scripts/train.py --model yolov13x --dataset aqua --experiment yolov13x_aqua_multi_gpu --epochs 150 --batch-size 8 --device 0,1,2,3
```

### 恢复训练 (从检查点继续)
```bash
# 从最后保存的检查点恢复训练
python scripts/train.py --model yolov8s --dataset aqua --experiment yolov8s_aqua_resume --resume experiments/yolov8s_aqua_baseline_*/weights/last.pt --epochs 200

# 从最佳模型恢复训练
python scripts/train.py --model yolo11m --dataset aqua --experiment yolo11m_aqua_resume --resume experiments/yolo11m_aqua_baseline_*/weights/best.pt --epochs 150
```

### 自定义配置训练
```bash
# 使用YOLOv8配置
python scripts/train.py --model yolov8n --dataset aqua --config yolov8_config --experiment yolov8n_aqua_custom --epochs 120

# 使用YOLOv11配置
python scripts/train.py --model yolo11s --dataset aqua --config yolov11_config --experiment yolo11s_aqua_custom --epochs 100

# 使用YOLOv13配置
python scripts/train.py --model yolov13m --dataset aqua --config yolov13_config --experiment yolov13m_aqua_custom --epochs 110
```

---

## 🎪 模型评估命令

### 单模型评估
```bash
# 评估YOLOv8n
python scripts/evaluate.py --model experiments/yolov8n_aqua_baseline_*/weights/best.pt --dataset aqua --experiment eval_yolov8n

# 评估YOLOv11s
python scripts/evaluate.py --model experiments/yolo11s_aqua_baseline_*/weights/best.pt --dataset aqua --experiment eval_yolo11s

# 评估YOLOv13n
python scripts/evaluate.py --model experiments\yolov13n_aqua_baseline_20250816_222004\yolov13n\weights\best.pt --dataset aqua --experiment eval_yolov13n
```

### 批量评估
```bash
# 批量评估多个模型
python scripts/evaluate.py --models experiments/yolov8n_aqua_baseline_*/weights/best.pt experiments/yolo11s_aqua_baseline_*/weights/best.pt experiments/yolov13m_aqua_baseline_*/weights/best.pt --dataset aqua --experiment batch_evaluation
```

### 模型比较
```bash
# 比较不同YOLO版本
python scripts/compare_models.py --evaluations eval_yolov8n eval_yolo11s eval_yolov13m --output version_comparison

# 比较同版本不同大小
python scripts/compare_models.py --evaluations eval_yolov8n eval_yolov8s eval_yolov8m --output yolov8_size_comparison
```

---

## 📊 性能优化建议

### 根据GPU内存选择批次大小：
- **4GB GPU**: batch-size 4-8
- **6GB GPU**: batch-size 8-12  
- **8GB GPU**: batch-size 12-16
- **12GB GPU**: batch-size 16-24
- **16GB+ GPU**: batch-size 24-32

### 根据精度要求选择模型：
- **快速验证**: yolov8n, yolo11n, yolov13n
- **平衡性能**: yolov8s, yolo11s, yolov13s  
- **高精度**: yolov8m, yolo11m, yolov13m
- **最高精度**: yolov8l, yolo11l, yolov13l, yolov8x, yolo11x, yolov13x

### 根据训练时间选择epochs：
- **快速测试**: 50-80 epochs
- **正常训练**: 100-120 epochs
- **高质量训练**: 150-200 epochs

---

## 🛠️ 故障排除

### 常见问题解决：
```bash
# 如果GPU内存不足，减少批次大小
python scripts/train.py --model yolov8s --dataset aqua --experiment yolov8s_aqua_small_batch --epochs 100 --batch-size 8 --device 0

# 如果训练速度慢，增加workers
python scripts/train.py --model yolo11n --dataset aqua --experiment yolo11n_aqua_fast --epochs 100 --batch-size 16 --workers 8 --device 0

# 如果精度不够，降低学习率
python scripts/train.py --model yolov13s --dataset aqua --experiment yolov13s_aqua_precise --epochs 120 --batch-size 12 --learning-rate 0.0003 --device 0
```

---

## 📝 使用提示

1. **首次训练建议使用nano版本快速验证**
2. **训练结果保存在 `experiments/` 目录下**
3. **可以同时运行多个实验，使用不同的实验名称**
4. **训练过程中可以按 Ctrl+C 安全停止**
5. **建议定期备份重要的训练结果**

---

**💡 提示**: 复制命令前请确保当前目录在项目根目录 `C:\Users\<USER>\Desktop\yolov13\`