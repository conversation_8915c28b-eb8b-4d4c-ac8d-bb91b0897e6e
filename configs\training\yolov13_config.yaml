# YOLOv13专用训练配置
epochs: 100
batch_size: 16
learning_rate: 0.01
optimizer: "AdamW"
scheduler: "cosine"
warmup_epochs: 3
weight_decay: 0.0005
momentum: 0.937

# YOLOv13数据增强设置（针对卫星图像优化）
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4
degrees: 0.0    # 卫星通常不旋转
translate: 0.1
scale: 0.5
shear: 0.0
perspective: 0.0
flipud: 0.0
fliplr: 0.5

# YOLOv13 Mosaic和Mixup设置（根据模型变体调整）
mosaic: 1.0
mixup: 0.0      # 基础设置，具体根据变体调整
copy_paste: 0.1

# 训练设置
amp: true
cache: false
workers: 4
save_period: 10
patience: 50
close_mosaic: 10
seed: 42

# YOLOv13特有设置
warmup_momentum: 0.8

# 变体特定设置（将在代码中动态调整）
# n: mixup=0.0
# s: mixup=0.05  
# m: mixup=0.10
# l: mixup=0.15
# x: mixup=0.20