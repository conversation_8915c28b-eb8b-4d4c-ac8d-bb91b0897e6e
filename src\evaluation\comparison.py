"""
模型比较模块
"""

from typing import Dict, List, Any, Optional
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path

class ModelComparator:
    """模型比较器"""
    
    def __init__(self):
        """初始化比较器"""
        pass
    
    def compare_metrics(self, 
                       results: List[Dict[str, Any]], 
                       metrics: List[str] = None) -> pd.DataFrame:
        """
        比较多个模型的评估指标
        
        Args:
            results: 评估结果列表
            metrics: 要比较的指标列表
            
        Returns:
            比较结果DataFrame
        """
        if metrics is None:
            metrics = ["mAP50-95", "mAP50", "precision", "recall", "f1_score"]
        
        comparison_data = []
        
        for result in results:
            model_name = result.get("model_name", "Unknown")
            row = {"Model": model_name}
            
            for metric in metrics:
                row[metric] = result.get(metric, None)
            
            comparison_data.append(row)
        
        return pd.DataFrame(comparison_data)
    
    def rank_models(self, 
                   comparison_df: pd.DataFrame, 
                   metric: str = "mAP50-95") -> pd.DataFrame:
        """
        根据指定指标对模型进行排名
        
        Args:
            comparison_df: 比较结果DataFrame
            metric: 排名依据的指标
            
        Returns:
            排名后的DataFrame
        """
        if metric not in comparison_df.columns:
            raise ValueError(f"指标 {metric} 不存在于比较结果中")
        
        # 按指标降序排序
        ranked_df = comparison_df.sort_values(by=metric, ascending=False, na_last=True)
        
        # 添加排名列
        ranked_df = ranked_df.reset_index(drop=True)
        ranked_df.insert(0, "Rank", range(1, len(ranked_df) + 1))
        
        return ranked_df
    
    def generate_comparison_plot(self, 
                               comparison_df: pd.DataFrame,
                               metrics: List[str] = None,
                               save_path: Optional[str] = None):
        """
        生成比较图表
        
        Args:
            comparison_df: 比较结果DataFrame
            metrics: 要绘制的指标列表
            save_path: 保存路径
        """
        if metrics is None:
            metrics = ["mAP50-95", "mAP50", "precision", "recall"]
        
        # 过滤存在的指标
        available_metrics = [m for m in metrics if m in comparison_df.columns]
        
        if not available_metrics:
            print("没有可用的指标进行绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        axes = axes.flatten()
        
        for i, metric in enumerate(available_metrics[:4]):
            ax = axes[i]
            
            # 过滤掉NaN值
            data = comparison_df[["Model", metric]].dropna()
            
            if not data.empty:
                ax.bar(data["Model"], data[metric])
                ax.set_title(f"{metric} Comparison")
                ax.set_ylabel(metric)
                ax.tick_params(axis='x', rotation=45)
            else:
                ax.text(0.5, 0.5, f"No data for {metric}", 
                       ha='center', va='center', transform=ax.transAxes)
        
        # 隐藏多余的子图
        for i in range(len(available_metrics), 4):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"比较图表已保存: {save_path}")
        else:
            plt.show()