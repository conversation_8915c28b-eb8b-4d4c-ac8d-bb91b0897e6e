"""
YOLO模型工厂
提供统一的模型创建接口
"""

from typing import Dict, Any, Optional
import logging

from .base_model import BaseYOLOModel
from .yolov8_model import YOLOv8Model
from .yolov11_model import YOLOv11Model
from .yolov13_model import YOLOv13Model

logger = logging.getLogger(__name__)

class YOLOModelFactory:
    """YOLO模型工厂类"""
    
    # 支持的模型版本
    SUPPORTED_VERSIONS = {
        "v8": YOLOv8Model,
        "v11": YOLOv11Model, 
        "v13": YOLOv13Model,
        "11": YOLOv11Model,  # 支持yolo11格式
    }
    
    # 支持的模型变体
    SUPPORTED_VARIANTS = ["n", "s", "m", "l", "x"]
    
    @classmethod
    def create_model(cls, 
                    version: str,
                    variant: str = "n", 
                    num_classes: int = 1,
                    pretrained: bool = True,
                    weights: Optional[str] = None) -> BaseYOLOModel:
        """
        创建YOLO模型
        
        Args:
            version: 模型版本 ("v8", "v11", "v13")
            variant: 模型变体 ("n", "s", "m", "l", "x")
            num_classes: 类别数量
            pretrained: 是否使用预训练权重
            weights: 自定义权重路径
            
        Returns:
            YOLO模型实例
            
        Raises:
            ValueError: 不支持的模型版本或变体
        """
        # 验证版本
        if version not in cls.SUPPORTED_VERSIONS:
            raise ValueError(f"不支持的模型版本: {version}. 支持的版本: {list(cls.SUPPORTED_VERSIONS.keys())}")
        
        # 验证变体
        if variant not in cls.SUPPORTED_VARIANTS:
            raise ValueError(f"不支持的模型变体: {variant}. 支持的变体: {cls.SUPPORTED_VARIANTS}")
        
        # 创建模型配置
        model_config = {
            "name": f"yolo{version}{variant}",
            "version": version,
            "variant": variant,
            "num_classes": num_classes,
            "pretrained": pretrained
        }
        
        # 获取模型类
        model_class = cls.SUPPORTED_VERSIONS[version]
        
        # 创建模型实例
        try:
            model = model_class(model_config, weights)
            logger.info(f"成功创建模型: {model}")
            return model
        except Exception as e:
            logger.error(f"创建模型失败: {e}")
            raise
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> BaseYOLOModel:
        """
        从配置字典创建模型
        
        Args:
            config: 模型配置字典
            
        Returns:
            YOLO模型实例
        """
        version = config.get("version", "v8")
        variant = config.get("variant", "n")
        num_classes = config.get("num_classes", 1)
        pretrained = config.get("pretrained", True)
        weights = config.get("weights", None)
        
        return cls.create_model(
            version=version,
            variant=variant,
            num_classes=num_classes,
            pretrained=pretrained,
            weights=weights
        )
    
    @classmethod
    def get_supported_models(cls) -> Dict[str, list]:
        """
        获取支持的模型列表
        
        Returns:
            支持的模型字典
        """
        return {
            "versions": list(cls.SUPPORTED_VERSIONS.keys()),
            "variants": cls.SUPPORTED_VARIANTS,
            "combinations": [
                f"{version}{variant}" 
                for version in cls.SUPPORTED_VERSIONS.keys()
                for variant in cls.SUPPORTED_VARIANTS
            ]
        }
    
    @classmethod
    def validate_model_name(cls, model_name: str) -> bool:
        """
        验证模型名称是否有效
        
        Args:
            model_name: 模型名称 (如 "yolov8n", "yolo11s", "yolov13x")
            
        Returns:
            是否有效
        """
        # 解析模型名称
        if not model_name.startswith("yolo"):
            return False
        
        # 提取版本和变体
        remainder = model_name[4:]  # 去掉 "yolo"
        
        # 查找版本 - 按长度排序，先匹配较长的版本号
        version = None
        sorted_versions = sorted(cls.SUPPORTED_VERSIONS.keys(), key=len, reverse=True)
        for v in sorted_versions:
            if remainder.startswith(v):
                version = v
                remainder = remainder[len(v):]
                break
        
        if version is None:
            return False
        
        # 检查变体
        if remainder not in cls.SUPPORTED_VARIANTS:
            return False
        
        return True
    
    @classmethod
    def parse_model_name(cls, model_name: str) -> Dict[str, str]:
        """
        解析模型名称
        
        Args:
            model_name: 模型名称
            
        Returns:
            解析结果字典
            
        Raises:
            ValueError: 无效的模型名称
        """
        if not cls.validate_model_name(model_name):
            raise ValueError(f"无效的模型名称: {model_name}")
        
        remainder = model_name[4:]  # 去掉 "yolo"
        
        # 查找版本 - 按长度排序，先匹配较长的版本号
        version = None
        sorted_versions = sorted(cls.SUPPORTED_VERSIONS.keys(), key=len, reverse=True)
        for v in sorted_versions:
            if remainder.startswith(v):
                version = v
                remainder = remainder[len(v):]
                break
        
        variant = remainder
        
        return {
            "version": version,
            "variant": variant,
            "full_name": model_name
        }