# 安装指南

## 系统要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU训练)
- 16GB+ RAM (推荐)

## 环境设置

### 1. 创建虚拟环境

```bash
# 使用conda (推荐)
conda create -n yolo-baseline python=3.11
conda activate yolo-baseline

# 或使用venv
python -m venv yolo-baseline
source yolo-baseline/bin/activate  # Linux/Mac
# 或
yolo-baseline\Scripts\activate     # Windows
```

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd yolo-satellite-baseline

# 安装基础依赖
pip install -r requirements.txt

# 安装项目包 (开发模式)
pip install -e .
```

### 3. 验证安装

```bash
# 运行框架测试
python scripts/test_framework.py

# 检查CUDA可用性
python -c "import torch; print(f'CUDA可用: {torch.cuda.is_available()}')"
```

## GPU支持

### NVIDIA GPU

```bash
# 检查CUDA版本
nvidia-smi

# 安装对应的PyTorch版本
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### Flash Attention (可选)

为了获得最佳性能，可以安装Flash Attention：

```bash
# 下载预编译wheel (Linux)
wget https://github.com/Dao-AILab/flash-attention/releases/download/v2.7.3/flash_attn-2.7.3+cu11torch2.2cxx11abiFALSE-cp311-cp311-linux_x86_64.whl
pip install flash_attn-2.7.3+cu11torch2.2cxx11abiFALSE-cp311-cp311-linux_x86_64.whl
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'ultralytics'**
   ```bash
   pip install ultralytics>=8.0.0
   ```

2. **CUDA out of memory**
   - 减少batch_size
   - 使用混合精度训练 (amp=True)

3. **模型下载失败**
   - 检查网络连接
   - 使用代理或镜像源

### 获取帮助

如果遇到问题，请：
1. 查看[常见问题](troubleshooting.md)
2. 搜索已有的[Issues](../../issues)
3. 创建新的Issue并提供详细信息