"""
统一评估器
提供多YOLO模型的统一评估接口
"""

from typing import Dict, Any, List, Optional
from pathlib import Path
import logging
import json
from datetime import datetime

from ..models.base_model import BaseYOLOModel
from ..utils.helpers import save_checkpoint, create_experiment_dir

logger = logging.getLogger(__name__)

class UnifiedEvaluator:
    """统一模型评估器"""
    
    def __init__(self, output_dir: str = "experiments/evaluations"):
        """
        初始化评估器
        
        Args:
            output_dir: 评估结果输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def evaluate_model(self,
                      model: BaseYOLOModel,
                      dataset_config: Dict[str, Any],
                      eval_config: Dict[str, Any],
                      experiment_name: Optional[str] = None) -> Dict[str, Any]:
        """
        评估单个模型
        
        Args:
            model: YOLO模型实例
            dataset_config: 数据集配置
            eval_config: 评估配置
            experiment_name: 实验名称
            
        Returns:
            评估结果字典
        """
        if experiment_name is None:
            experiment_name = f"eval_{model.get_model_info()['name']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"📊 开始评估模型: {model}")
        
        try:
            # 执行评估
            eval_results = model.evaluate(dataset_config, eval_config)
            
            # 添加模型信息和评估配置
            eval_results.update({
                "model_info": model.get_model_info(),
                "dataset_config": dataset_config,
                "eval_config": eval_config,
                "experiment_name": experiment_name,
                "timestamp": datetime.now().isoformat()
            })
            
            # 保存评估结果
            self._save_evaluation_results(eval_results, experiment_name)
            
            logger.info(f"✅ 模型评估完成: {model}")
            logger.info(f"📊 mAP50-95: {eval_results.get('mAP50-95', 'N/A'):.4f}")
            logger.info(f"📊 mAP50: {eval_results.get('mAP50', 'N/A'):.4f}")
            
            return eval_results
            
        except Exception as e:
            logger.error(f"❌ 模型评估失败: {e}")
            raise
    
    def batch_evaluate(self,
                      models: List[BaseYOLOModel],
                      dataset_config: Dict[str, Any], 
                      eval_config: Dict[str, Any],
                      experiment_name: Optional[str] = None) -> Dict[str, Any]:
        """
        批量评估多个模型
        
        Args:
            models: YOLO模型列表
            dataset_config: 数据集配置
            eval_config: 评估配置
            experiment_name: 实验名称
            
        Returns:
            批量评估结果字典
        """
        if experiment_name is None:
            experiment_name = f"batch_eval_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"📊 开始批量评估 {len(models)} 个模型")
        
        batch_results = {
            "experiment_name": experiment_name,
            "timestamp": datetime.now().isoformat(),
            "dataset_config": dataset_config,
            "eval_config": eval_config,
            "models_count": len(models),
            "results": {},
            "summary": {}
        }
        
        success_count = 0
        failed_models = []
        
        for i, model in enumerate(models):
            model_name = model.get_model_info()["name"]
            logger.info(f"📊 评估模型 {i+1}/{len(models)}: {model_name}")
            
            try:
                # 评估单个模型
                model_experiment = f"{experiment_name}_{model_name}"
                eval_result = self.evaluate_model(
                    model=model,
                    dataset_config=dataset_config,
                    eval_config=eval_config,
                    experiment_name=model_experiment
                )
                
                batch_results["results"][model_name] = eval_result
                success_count += 1
                
            except Exception as e:
                logger.error(f"❌ 模型 {model_name} 评估失败: {e}")
                failed_models.append(model_name)
                batch_results["results"][model_name] = {
                    "status": "failed",
                    "error": str(e)
                }
        
        # 生成汇总统计
        batch_results["summary"] = {
            "success_count": success_count,
            "failed_count": len(failed_models),
            "failed_models": failed_models,
            "success_rate": success_count / len(models) if models else 0
        }
        
        # 保存批量评估结果
        self._save_batch_results(batch_results, experiment_name)
        
        logger.info(f"🎉 批量评估完成")
        logger.info(f"📊 成功: {success_count}/{len(models)}")
        if failed_models:
            logger.warning(f"⚠️  失败的模型: {failed_models}")
        
        return batch_results
    
    def compare_models(self,
                      eval_results: List[Dict[str, Any]],
                      metrics: List[str] = None,
                      experiment_name: Optional[str] = None) -> Dict[str, Any]:
        """
        比较多个模型的评估结果
        
        Args:
            eval_results: 评估结果列表
            metrics: 要比较的指标列表
            experiment_name: 实验名称
            
        Returns:
            比较结果字典
        """
        if metrics is None:
            metrics = ["mAP50-95", "mAP50", "mAP75", "precision", "recall", "f1_score"]
        
        if experiment_name is None:
            experiment_name = f"comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        logger.info(f"📊 开始模型比较分析")
        
        comparison = {
            "experiment_name": experiment_name,
            "timestamp": datetime.now().isoformat(),
            "metrics": metrics,
            "models_count": len(eval_results),
            "comparison_table": {},
            "rankings": {},
            "best_models": {},
            "analysis": {}
        }
        
        # 构建比较表
        for metric in metrics:
            comparison["comparison_table"][metric] = {}
            metric_values = []
            
            for result in eval_results:
                model_name = result.get("model_name", "unknown")
                metric_value = result.get(metric, None)
                
                if metric_value is not None:
                    comparison["comparison_table"][metric][model_name] = metric_value
                    metric_values.append((model_name, metric_value))
            
            # 按指标排序
            metric_values.sort(key=lambda x: x[1], reverse=True)
            comparison["rankings"][metric] = [name for name, _ in metric_values]
            
            # 最佳模型
            if metric_values:
                best_model, best_value = metric_values[0]
                comparison["best_models"][metric] = {
                    "model": best_model,
                    "value": best_value
                }
        
        # 综合分析
        if eval_results:
            comparison["analysis"] = self._analyze_comparison(comparison, eval_results)
        
        # 保存比较结果
        self._save_comparison_results(comparison, experiment_name)
        
        logger.info(f"✅ 模型比较完成")
        for metric in metrics:
            if metric in comparison["best_models"]:
                best = comparison["best_models"][metric]
                logger.info(f"🏆 {metric} 最佳: {best['model']} ({best['value']:.4f})")
        
        return comparison
    
    def _analyze_comparison(self, comparison: Dict[str, Any], eval_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析比较结果"""
        analysis = {
            "overall_best": None,
            "efficiency_analysis": {},
            "trade_offs": {},
            "recommendations": []
        }
        
        # 计算综合得分（基于主要指标的加权平均）
        weights = {"mAP50-95": 0.4, "mAP50": 0.3, "precision": 0.15, "recall": 0.15}
        overall_scores = {}
        
        for result in eval_results:
            model_name = result.get("model_name", "unknown")
            score = 0
            total_weight = 0
            
            for metric, weight in weights.items():
                if metric in result and result[metric] is not None:
                    score += result[metric] * weight
                    total_weight += weight
            
            if total_weight > 0:
                overall_scores[model_name] = score / total_weight
        
        if overall_scores:
            best_overall = max(overall_scores.items(), key=lambda x: x[1])
            analysis["overall_best"] = {
                "model": best_overall[0],
                "score": best_overall[1]
            }
        
        # 效率分析（如果有模型信息）
        for result in eval_results:
            model_name = result.get("model_name", "unknown")
            model_info = result.get("model_info", {})
            
            if "total" in model_info:  # 参数数量
                params = model_info["total"]
                map50_95 = result.get("mAP50-95", 0)
                if params > 0:
                    efficiency = map50_95 / (params / 1e6)  # mAP per million parameters
                    analysis["efficiency_analysis"][model_name] = {
                        "parameters_M": params / 1e6,
                        "mAP50-95": map50_95,
                        "efficiency": efficiency
                    }
        
        # 生成建议
        if analysis["overall_best"]:
            analysis["recommendations"].append(f"综合性能最佳: {analysis['overall_best']['model']}")
        
        if analysis["efficiency_analysis"]:
            efficient_models = sorted(analysis["efficiency_analysis"].items(), 
                                    key=lambda x: x[1]["efficiency"], reverse=True)
            if efficient_models:
                analysis["recommendations"].append(f"效率最佳: {efficient_models[0][0]}")
        
        return analysis
    
    def _save_evaluation_results(self, results: Dict[str, Any], experiment_name: str):
        """保存评估结果"""
        eval_dir = self.output_dir / experiment_name
        eval_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存JSON格式结果
        results_file = eval_dir / "evaluation_results.json"
        save_checkpoint(results, str(results_file), format="json")
        
        # 保存简化的汇总
        summary = {
            "model_name": results.get("model_name", "unknown"),
            "mAP50-95": results.get("mAP50-95", None),
            "mAP50": results.get("mAP50", None),
            "mAP75": results.get("mAP75", None),
            "precision": results.get("precision", None),
            "recall": results.get("recall", None),
            "f1_score": results.get("f1_score", None),
            "timestamp": results.get("timestamp", None)
        }
        
        summary_file = eval_dir / "summary.json"
        save_checkpoint(summary, str(summary_file), format="json")
        
        logger.info(f"📁 评估结果已保存: {eval_dir}")
    
    def _save_batch_results(self, results: Dict[str, Any], experiment_name: str):
        """保存批量评估结果"""
        batch_dir = self.output_dir / experiment_name
        batch_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存完整结果
        results_file = batch_dir / "batch_evaluation_results.json"
        save_checkpoint(results, str(results_file), format="json")
        
        logger.info(f"📁 批量评估结果已保存: {batch_dir}")
    
    def _save_comparison_results(self, results: Dict[str, Any], experiment_name: str):
        """保存比较结果"""
        comp_dir = self.output_dir / experiment_name
        comp_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存比较结果
        results_file = comp_dir / "model_comparison.json"
        save_checkpoint(results, str(results_file), format="json")
        
        logger.info(f"📁 模型比较结果已保存: {comp_dir}")
    
    def load_evaluation_results(self, experiment_name: str) -> Optional[Dict[str, Any]]:
        """
        加载评估结果
        
        Args:
            experiment_name: 实验名称
            
        Returns:
            评估结果字典，如果不存在则返回None
        """
        results_file = self.output_dir / experiment_name / "evaluation_results.json"
        
        if not results_file.exists():
            logger.warning(f"评估结果文件不存在: {results_file}")
            return None
        
        try:
            with open(results_file, 'r', encoding='utf-8') as f:
                results = json.load(f)
            logger.info(f"✅ 加载评估结果: {experiment_name}")
            return results
        except Exception as e:
            logger.error(f"❌ 加载评估结果失败: {e}")
            return None
    
    def list_evaluations(self) -> List[str]:
        """
        列出所有评估实验
        
        Returns:
            实验名称列表
        """
        if not self.output_dir.exists():
            return []
        
        experiments = []
        for item in self.output_dir.iterdir():
            if item.is_dir() and (item / "evaluation_results.json").exists():
                experiments.append(item.name)
        
        return sorted(experiments)