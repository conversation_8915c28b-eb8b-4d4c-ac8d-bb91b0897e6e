#!/usr/bin/env python3
"""
模型对比脚本
快速对比多个YOLO模型的性能
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils import setup_logger
from src.evaluation import UnifiedEvaluator
import logging

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="YOLO模型性能对比脚本")
    
    parser.add_argument("--evaluations", type=str, nargs="+", required=True,
                       help="评估实验名称列表")
    parser.add_argument("--metrics", type=str, nargs="*",
                       default=["mAP50-95", "mAP50", "precision", "recall", "f1_score"],
                       help="要比较的指标")
    parser.add_argument("--output", type=str, default="model_comparison",
                       help="输出比较实验名称")
    parser.add_argument("--save-plot", type=str, default=None,
                       help="保存比较图表路径")
    parser.add_argument("--verbose", action="store_true",
                       help="详细输出")
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger("compare", level=log_level)
    
    logger.info("📊 启动YOLO模型对比系统")
    logger.info(f"比较评估: {args.evaluations}")
    
    try:
        # 初始化评估器
        evaluator = UnifiedEvaluator()
        
        # 加载评估结果
        eval_results = []
        for eval_name in args.evaluations:
            logger.info(f"📂 加载评估结果: {eval_name}")
            result = evaluator.load_evaluation_results(eval_name)
            if result:
                eval_results.append(result)
                logger.info(f"✅ 成功加载: {result.get('model_name', 'Unknown')}")
            else:
                logger.warning(f"⚠️  无法加载评估结果: {eval_name}")
        
        if not eval_results:
            logger.error("❌ 没有成功加载任何评估结果")
            return 1
        
        # 执行比较
        logger.info(f"🔍 开始比较 {len(eval_results)} 个模型...")
        comparison = evaluator.compare_models(
            eval_results=eval_results,
            metrics=args.metrics,
            experiment_name=args.output
        )
        
        # 显示结果
        print(f"\n{'='*80}")
        print(f"🏆 YOLO模型性能对比结果")
        print(f"{'='*80}")
        print(f"比较模型数量: {comparison['models_count']}")
        print(f"比较指标: {', '.join(comparison['metrics'])}")
        
        # 显示各指标最佳模型
        if comparison["best_models"]:
            print(f"\n📊 各指标最佳模型:")
            print(f"{'-'*60}")
            for metric, best in comparison["best_models"].items():
                print(f"{metric:15s}: {best['model']:20s} ({best['value']:.4f})")
        
        # 显示综合分析
        if comparison["analysis"]["overall_best"]:
            overall = comparison["analysis"]["overall_best"]
            print(f"\n👑 综合最佳模型: {overall['model']} (综合得分: {overall['score']:.4f})")
        
        # 显示效率分析
        if comparison["analysis"]["efficiency_analysis"]:
            print(f"\n⚡ 效率分析 (mAP50-95 / 百万参数):")
            print(f"{'-'*60}")
            eff_sorted = sorted(comparison["analysis"]["efficiency_analysis"].items(),
                              key=lambda x: x[1]["efficiency"], reverse=True)
            for model_name, eff_data in eff_sorted:
                print(f"{model_name:15s}: {eff_data['efficiency']:.3f} "
                      f"(mAP: {eff_data['mAP50-95']:.3f}, 参数: {eff_data['parameters_M']:.1f}M)")
        
        # 显示建议
        if comparison["analysis"]["recommendations"]:
            print(f"\n💡 推荐建议:")
            for i, rec in enumerate(comparison["analysis"]["recommendations"], 1):
                print(f"  {i}. {rec}")
        
        # 显示详细比较表
        print(f"\n📋 详细比较表:")
        print(f"{'-'*80}")
        header = f"{'Model':15s}"
        for metric in comparison["metrics"]:
            header += f"{metric:>12s}"
        print(header)
        print("-" * len(header))
        
        for model_name in comparison["comparison_table"][comparison["metrics"][0]].keys():
            row = f"{model_name:15s}"
            for metric in comparison["metrics"]:
                value = comparison["comparison_table"][metric].get(model_name, None)
                if value is not None:
                    row += f"{value:12.4f}"
                else:
                    row += f"{'N/A':>12s}"
            print(row)
        
        # 生成比较图表
        if args.save_plot:
            try:
                from src.evaluation.comparison import ModelComparator
                import pandas as pd
                
                # 创建DataFrame
                comparison_data = []
                for model_name in comparison["comparison_table"][comparison["metrics"][0]].keys():
                    row = {"Model": model_name}
                    for metric in comparison["metrics"]:
                        row[metric] = comparison["comparison_table"][metric].get(model_name, None)
                    comparison_data.append(row)
                
                df = pd.DataFrame(comparison_data)
                
                # 生成图表
                comparator = ModelComparator()
                comparator.generate_comparison_plot(df, comparison["metrics"], args.save_plot)
                
            except ImportError:
                logger.warning("⚠️  无法生成图表，请安装pandas和matplotlib")
        
        logger.info("🎉 模型对比完成！")
        return 0
        
    except Exception as e:
        logger.error(f"❌ 模型对比过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())