{"model_name": "yolov13n", "mAP50-95": 0.6289571994671994, "mAP50": 0.7605333333333333, "mAP75": 0.7605333333333333, "precision": 0.9778522592812099, "recall": 0.6190476190476191, "f1_score": 0.7581403456826099, "results_dict": {"metrics/precision(B)": 0.9778522592812099, "metrics/recall(B)": 0.6190476190476191, "metrics/mAP50(B)": 0.7605333333333333, "metrics/mAP75(B)": 0.7605333333333333, "metrics/mAP50-95(B)": 0.6289571994671994, "fitness": 0.6289571994671994}, "model_info": {"name": "YOLOvv13n", "version": "v13", "variant": "n", "num_classes": 1, "is_trained": false, "weights": "experiments\\yolov13n_aqua_baseline_20250816_222004\\yolov13n\\weights\\best.pt", "total": 2448090, "trainable": 0, "frozen": 2448090}, "dataset_config": {"name": "aqua", "path": "datasets/Aqua_YOLO", "config_file": "aqua_dataset.yaml", "image_size": 640, "num_classes": 1}, "eval_config": {"split": "test", "conf_threshold": 0.5, "iou_threshold": 0.7, "save_json": false, "plots": true}, "experiment_name": "eval_yolov13n", "timestamp": "2025-08-16T22:37:10.626120"}