#!/usr/bin/env python3
"""
环境检查脚本
检查项目运行环境是否正确配置
"""

import sys
import subprocess
from pathlib import Path
import importlib.util

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 8:
        print("   ✅ Python版本符合要求 (>=3.8)")
        return True
    else:
        print("   ❌ Python版本过低，需要 >=3.8")
        return False

def check_package(package_name, import_name=None):
    """检查包是否安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        spec = importlib.util.find_spec(import_name)
        if spec is not None:
            module = importlib.import_module(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"   ✅ {package_name}: {version}")
            return True
        else:
            print(f"   ❌ {package_name}: 未安装")
            return False
    except Exception as e:
        print(f"   ❌ {package_name}: 导入错误 - {e}")
        return False

def check_cuda():
    """检查CUDA可用性"""
    print("🔥 检查CUDA...")
    try:
        import torch
        if torch.cuda.is_available():
            print(f"   ✅ CUDA可用: {torch.cuda.get_device_name(0)}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            return True
        else:
            print("   ⚠️  CUDA不可用，将使用CPU")
            return False
    except:
        print("   ❌ 无法检查CUDA状态")
        return False

def check_paths():
    """检查关键路径"""
    print("📁 检查项目路径...")
    paths_to_check = [
        "src",
        "configs", 
        "datasets/Aqua_YOLO",
        "scripts",
        "experiments"
    ]
    
    all_exist = True
    for path_str in paths_to_check:
        path = Path(path_str)
        if path.exists():
            print(f"   ✅ {path_str}")
        else:
            print(f"   ❌ {path_str}: 不存在")
            all_exist = False
    
    return all_exist

def check_dataset():
    """检查数据集"""
    print("📊 检查数据集...")
    dataset_path = Path("datasets/Aqua_YOLO")
    
    if not dataset_path.exists():
        print("   ❌ 数据集目录不存在")
        return False
    
    required_dirs = ["images/train", "images/val", "labels/train", "labels/val"]
    all_exist = True
    
    for dir_name in required_dirs:
        dir_path = dataset_path / dir_name
        if dir_path.exists():
            count = len(list(dir_path.glob("*")))
            print(f"   ✅ {dir_name}: {count} 文件")
        else:
            print(f"   ❌ {dir_name}: 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主函数"""
    print("🔍 开始环境检查")
    print("=" * 50)
    
    checks = []
    
    # 检查Python版本
    checks.append(check_python_version())
    print()
    
    # 检查核心包
    print("📦 检查核心依赖...")
    core_packages = [
        ("torch", "torch"),
        ("torchvision", "torchvision"), 
        ("ultralytics", "ultralytics"),
        ("opencv-python", "cv2"),
        ("numpy", "numpy"),
        ("matplotlib", "matplotlib"),
        ("pyyaml", "yaml"),
        ("pandas", "pandas")
    ]
    
    package_checks = []
    for pkg_name, import_name in core_packages:
        package_checks.append(check_package(pkg_name, import_name))
    
    checks.append(all(package_checks))
    print()
    
    # 检查CUDA
    checks.append(check_cuda())
    print()
    
    # 检查路径
    checks.append(check_paths())
    print()
    
    # 检查数据集
    checks.append(check_dataset())
    print()
    
    # 总结
    print("=" * 50)
    passed = sum(checks)
    total = len(checks)
    
    if passed == total:
        print("🎉 所有检查通过！环境配置正确。")
        return 0
    else:
        print(f"⚠️  {passed}/{total} 项检查通过，请修复上述问题。")
        return 1

if __name__ == "__main__":
    exit(main())
