#!/usr/bin/env python3
"""
数据准备脚本

整合原有的数据转换和可视化功能
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils import setup_logger
import logging

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="数据准备脚本")
    
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 转换命令
    convert_parser = subparsers.add_parser("convert", help="转换数据集格式")
    convert_parser.add_argument("--source", type=str, required=True,
                               help="源数据集目录")
    convert_parser.add_argument("--output", type=str, required=True,
                               help="输出YOLO格式目录")
    convert_parser.add_argument("--format", type=str, default="unity",
                               choices=["unity", "coco", "voc"],
                               help="源数据格式")
    convert_parser.add_argument("--train-ratio", type=float, default=0.7,
                               help="训练集比例")
    convert_parser.add_argument("--val-ratio", type=float, default=0.2,
                               help="验证集比例")
    
    # 可视化命令
    viz_parser = subparsers.add_parser("visualize", help="可视化数据集")
    viz_parser.add_argument("--dataset", type=str, required=True,
                           help="YOLO数据集目录")
    viz_parser.add_argument("--split", type=str, default="train",
                           choices=["train", "val", "test"],
                           help="数据集分割")
    viz_parser.add_argument("--samples", type=int, default=4,
                           help="显示样本数量")
    viz_parser.add_argument("--save", type=str, default=None,
                           help="保存可视化图像路径")
    
    # 统计命令
    stats_parser = subparsers.add_parser("stats", help="数据集统计")
    stats_parser.add_argument("--dataset", type=str, required=True,
                             help="YOLO数据集目录")
    
    # 验证命令
    validate_parser = subparsers.add_parser("validate", help="验证数据集")
    validate_parser.add_argument("--dataset", type=str, required=True,
                                help="YOLO数据集目录")
    
    parser.add_argument("--verbose", action="store_true",
                       help="详细输出")
    
    return parser.parse_args()

def convert_unity_to_yolo(source_dir: str, output_dir: str, 
                         train_ratio: float, val_ratio: float):
    """转换Unity Solo格式到YOLO格式"""
    # 复用原有的转换代码
    import json
    import shutil
    from pathlib import Path
    from tqdm import tqdm
    
    logger = logging.getLogger(__name__)
    
    class AquaToYOLOConverter:
        """Convert Aqua dataset from Unity Solo JSON format to YOLO format."""
        
        def __init__(self, source_dir: str, output_dir: str):
            self.source_dir = Path(source_dir)
            self.output_dir = Path(output_dir)
            
            # Class mapping - Aqua satellite is the only class
            self.class_mapping = {
                "Observation Satellite - Aqua": 0
            }
            
            # Image dimensions from the JSON files
            self.img_width = 1920
            self.img_height = 1080
            
        def create_output_structure(self):
            """Create YOLO dataset directory structure."""
            (self.output_dir / "images" / "train").mkdir(parents=True, exist_ok=True)
            (self.output_dir / "images" / "val").mkdir(parents=True, exist_ok=True)
            (self.output_dir / "images" / "test").mkdir(parents=True, exist_ok=True)
            (self.output_dir / "labels" / "train").mkdir(parents=True, exist_ok=True)
            (self.output_dir / "labels" / "val").mkdir(parents=True, exist_ok=True)
            (self.output_dir / "labels" / "test").mkdir(parents=True, exist_ok=True)
            
            logger.info(f"创建输出目录结构: {self.output_dir}")
        
        def parse_json_annotation(self, json_path: Path):
            """Parse Unity Solo JSON annotation file."""
            try:
                with open(json_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                annotations = []
                
                captures = data.get('captures', [])
                for capture in captures:
                    if capture.get('@type') == 'type.unity.com/unity.solo.RGBCamera':
                        annotation_list = capture.get('annotations', [])
                        for annotation in annotation_list:
                            if annotation.get('@type') == 'type.unity.com/unity.solo.BoundingBox2DAnnotation':
                                values = annotation.get('values', [])
                                for value in values:
                                    label_name = value.get('labelName', '')
                                    if label_name in self.class_mapping:
                                        origin = value.get('origin', [0, 0])
                                        dimension = value.get('dimension', [0, 0])
                                        
                                        annotations.append({
                                            'class_name': label_name,
                                            'class_id': self.class_mapping[label_name],
                                            'x': origin[0],
                                            'y': origin[1],
                                            'width': dimension[0],
                                            'height': dimension[1]
                                        })
                
                return annotations
                
            except Exception as e:
                logger.error(f"解析JSON文件错误 {json_path}: {e}")
                return []
        
        def convert_to_yolo_format(self, annotations):
            """Convert bounding box annotations to YOLO format."""
            yolo_lines = []
            
            for ann in annotations:
                x_center = (ann['x'] + ann['width'] / 2) / self.img_width
                y_center = (ann['y'] + ann['height'] / 2) / self.img_height
                width_norm = ann['width'] / self.img_width
                height_norm = ann['height'] / self.img_height
                
                # Ensure values are within [0, 1] range
                x_center = max(0, min(1, x_center))
                y_center = max(0, min(1, y_center))
                width_norm = max(0, min(1, width_norm))
                height_norm = max(0, min(1, height_norm))
                
                yolo_line = f"{ann['class_id']} {x_center:.6f} {y_center:.6f} {width_norm:.6f} {height_norm:.6f}"
                yolo_lines.append(yolo_line)
            
            return yolo_lines
        
        def split_dataset(self, file_list, train_ratio: float = 0.7, val_ratio: float = 0.2):
            """Split dataset into train, validation and test sets."""
            file_list = sorted(file_list)
            
            total_files = len(file_list)
            train_split = int(total_files * train_ratio)
            val_split = int(total_files * (train_ratio + val_ratio))
            
            train_files = file_list[:train_split]
            val_files = file_list[train_split:val_split]
            test_files = file_list[val_split:]
            
            return train_files, val_files, test_files
        
        def process_files(self, json_files, split: str):
            """Process a list of JSON files and convert them to YOLO format."""
            processed_count = 0
            skipped_count = 0
            
            for json_file in tqdm(json_files, desc=f"处理 {split} 文件"):
                base_name = json_file.stem.replace('.frame_data', '')
                img_file = json_file.parent / f"{base_name}.camera.png"
                
                if not img_file.exists():
                    logger.warning(f"图像文件不存在: {img_file}")
                    skipped_count += 1
                    continue
                
                annotations = self.parse_json_annotation(json_file)
                yolo_lines = self.convert_to_yolo_format(annotations)
                
                img_output = self.output_dir / "images" / split / f"{base_name}.png"
                label_output = self.output_dir / "labels" / split / f"{base_name}.txt"
                
                shutil.copy2(img_file, img_output)
                
                with open(label_output, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(yolo_lines))
                    if yolo_lines:
                        f.write('\n')
                
                processed_count += 1
            
            logger.info(f"处理完成 {split}: {processed_count} 个文件，跳过 {skipped_count} 个文件")
        
        def create_dataset_yaml(self):
            """Create YOLO dataset configuration file."""
            yaml_content = f"""# Aqua Satellite Dataset Configuration
# Converted from Unity Solo format to YOLO format

# Dataset paths (relative to this file)
path: {self.output_dir.absolute()}
train: images/train
val: images/val
test: images/test

# Number of classes
nc: {len(self.class_mapping)}

# Class names
names:
"""
            
            for class_name, class_id in self.class_mapping.items():
                yaml_content += f"  {class_id}: {class_name}\n"
            
            yaml_path = self.output_dir / "aqua_dataset.yaml"
            with open(yaml_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            
            logger.info(f"创建数据集配置文件: {yaml_path}")
        
        def convert(self, train_ratio: float = 0.7, val_ratio: float = 0.2):
            """Convert the entire Aqua dataset to YOLO format."""
            logger.info("开始Aqua数据集转换...")
            logger.info(f"源目录: {self.source_dir}")
            logger.info(f"输出目录: {self.output_dir}")
            
            self.create_output_structure()
            
            json_files = list(self.source_dir.glob("*.frame_data.json"))
            
            if not json_files:
                raise ValueError(f"在源目录中未找到JSON标注文件: {self.source_dir}")
            
            logger.info(f"找到 {len(json_files)} 个标注文件")
            
            train_files, val_files, test_files = self.split_dataset(json_files, train_ratio, val_ratio)
            test_ratio = 1 - train_ratio - val_ratio
            logger.info(f"数据分割: {len(train_files)} 训练 ({train_ratio:.1f}), {len(val_files)} 验证 ({val_ratio:.1f}), {len(test_files)} 测试 ({test_ratio:.1f})")
            
            self.process_files(train_files, "train")
            self.process_files(val_files, "val")
            self.process_files(test_files, "test")
            
            self.create_dataset_yaml()
            
            logger.info("转换完成！")
            logger.info(f"数据集保存到: {self.output_dir}")
    
    # 执行转换
    converter = AquaToYOLOConverter(source_dir, output_dir)
    converter.convert(train_ratio, val_ratio)

def visualize_dataset(dataset_dir: str, split: str, samples: int, save_path: str = None):
    """可视化数据集"""
    # 复用原有的可视化代码
    import cv2
    import numpy as np
    import matplotlib.pyplot as plt
    import matplotlib.patches as patches
    import yaml
    import random
    from pathlib import Path
    
    logger = logging.getLogger(__name__)
    
    class YOLODatasetVisualizer:
        """Visualize YOLO format dataset with bounding boxes."""
        
        def __init__(self, dataset_path: str):
            self.dataset_path = Path(dataset_path)
            self.class_names = {}
            self.colors = {}
            
            self.load_dataset_config()
            self.generate_colors()
        
        def load_dataset_config(self):
            """Load dataset configuration from YAML file."""
            yaml_files = list(self.dataset_path.glob("*.yaml"))
            if not yaml_files:
                logger.warning("未找到YAML配置文件，使用默认类别名称")
                self.class_names = {0: "Unknown"}
                return
            
            yaml_file = yaml_files[0]
            try:
                with open(yaml_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                self.class_names = config.get('names', {0: "Unknown"})
                logger.info(f"加载数据集配置: {yaml_file}")
                logger.info(f"类别: {self.class_names}")
                
            except Exception as e:
                logger.error(f"加载YAML配置错误: {e}")
                self.class_names = {0: "Unknown"}
        
        def generate_colors(self):
            """Generate distinct colors for each class."""
            np.random.seed(42)
            for class_id in self.class_names.keys():
                color = np.random.randint(0, 255, 3)
                self.colors[class_id] = tuple(map(int, color))
        
        def parse_yolo_annotation(self, label_file: Path, img_width: int, img_height: int):
            """Parse YOLO format annotation file."""
            annotations = []
            
            if not label_file.exists():
                return annotations
            
            try:
                with open(label_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for line in lines:
                    line = line.strip()
                    if not line:
                        continue
                    
                    parts = line.split()
                    if len(parts) != 5:
                        continue
                    
                    class_id = int(parts[0])
                    x_center = float(parts[1])
                    y_center = float(parts[2])
                    width = float(parts[3])
                    height = float(parts[4])
                    
                    # Convert YOLO format to pixel coordinates
                    x_center_px = x_center * img_width
                    y_center_px = y_center * img_height
                    width_px = width * img_width
                    height_px = height * img_height
                    
                    x1 = int(x_center_px - width_px / 2)
                    y1 = int(y_center_px - height_px / 2)
                    x2 = int(x_center_px + width_px / 2)
                    y2 = int(y_center_px + height_px / 2)
                    
                    annotations.append({
                        'class_id': class_id,
                        'class_name': self.class_names.get(class_id, f"Class_{class_id}"),
                        'bbox': (x1, y1, x2, y2),
                        'confidence': 1.0
                    })
            
            except Exception as e:
                logger.error(f"解析标注文件错误 {label_file}: {e}")
            
            return annotations
        
        def draw_bounding_boxes(self, image: np.ndarray, annotations):
            """Draw bounding boxes on image."""
            img_with_boxes = image.copy()
            
            for ann in annotations:
                class_id = ann['class_id']
                class_name = ann['class_name']
                x1, y1, x2, y2 = ann['bbox']
                color = self.colors.get(class_id, (0, 255, 0))
                
                cv2.rectangle(img_with_boxes, (x1, y1), (x2, y2), color, 2)
                
                label = f"{class_name}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
                
                cv2.rectangle(img_with_boxes, 
                             (x1, y1 - label_size[1] - 10), 
                             (x1 + label_size[0], y1), 
                             color, -1)
                
                cv2.putText(img_with_boxes, label, (x1, y1 - 5), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            return img_with_boxes
        
        def visualize_multiple(self, split: str = "train", num_samples: int = 4, 
                              save_path: str = None):
            """Visualize multiple samples in a grid."""
            img_dir = self.dataset_path / "images" / split
            label_dir = self.dataset_path / "labels" / split
            
            if not img_dir.exists():
                logger.error(f"图像目录不存在: {img_dir}")
                return
            
            img_files = list(img_dir.glob("*.png")) + list(img_dir.glob("*.jpg")) + list(img_dir.glob("*.jpeg"))
            
            if not img_files:
                logger.error(f"在目录中未找到图像文件: {img_dir}")
                return
            
            sample_indices = random.sample(range(len(img_files)), min(num_samples, len(img_files)))
            
            cols = min(2, num_samples)
            rows = (num_samples + cols - 1) // cols
            
            fig, axes = plt.subplots(rows, cols, figsize=(15, 5*rows))
            if rows == 1 and cols == 1:
                axes = [axes]
            elif rows == 1 or cols == 1:
                axes = axes.flatten()
            else:
                axes = axes.flatten()
            
            for i, sample_idx in enumerate(sample_indices):
                img_file = img_files[sample_idx]
                label_file = label_dir / f"{img_file.stem}.txt"
                
                image = cv2.imread(str(img_file))
                if image is None:
                    continue
                
                img_height, img_width = image.shape[:2]
                annotations = self.parse_yolo_annotation(label_file, img_width, img_height)
                img_with_boxes = self.draw_bounding_boxes(image, annotations)
                img_rgb = cv2.cvtColor(img_with_boxes, cv2.COLOR_BGR2RGB)
                
                ax = axes[i] if len(sample_indices) > 1 else axes[0]
                ax.imshow(img_rgb)
                ax.set_title(f"{img_file.name}\\n{len(annotations)} annotations")
                ax.axis('off')
            
            # Hide unused subplots
            for i in range(len(sample_indices), len(axes)):
                axes[i].axis('off')
            
            plt.suptitle(f"Dataset: {self.dataset_path.name} | Split: {split}")
            plt.tight_layout()
            
            if save_path:
                plt.savefig(save_path, dpi=150, bbox_inches='tight')
                logger.info(f"可视化结果保存到: {save_path}")
            else:
                plt.show()
    
    # 执行可视化
    visualizer = YOLODatasetVisualizer(dataset_dir)
    visualizer.visualize_multiple(split, samples, save_path)

def show_dataset_stats(dataset_dir: str):
    """显示数据集统计信息"""
    from pathlib import Path
    import yaml
    
    logger = logging.getLogger(__name__)
    dataset_path = Path(dataset_dir)
    
    # 加载配置
    yaml_files = list(dataset_path.glob("*.yaml"))
    class_names = {0: "Unknown"}
    
    if yaml_files:
        try:
            with open(yaml_files[0], 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            class_names = config.get('names', {0: "Unknown"})
        except:
            pass
    
    print(f"\n{'='*60}")
    print(f"数据集统计: {dataset_path.name}")
    print(f"{'='*60}")
    
    for split in ['train', 'val', 'test']:
        img_dir = dataset_path / "images" / split
        label_dir = dataset_path / "labels" / split
        
        if not img_dir.exists():
            continue
        
        img_files = list(img_dir.glob("*.png")) + list(img_dir.glob("*.jpg"))
        label_files = list(label_dir.glob("*.txt"))
        
        total_annotations = 0
        class_counts = {}
        
        for label_file in label_files:
            try:
                with open(label_file, 'r') as f:
                    lines = f.readlines()
                
                for line in lines:
                    line = line.strip()
                    if line:
                        class_id = int(line.split()[0])
                        class_counts[class_id] = class_counts.get(class_id, 0) + 1
                        total_annotations += 1
            except:
                continue
        
        print(f"\n{split.upper()} 分割:")
        print(f"  图像: {len(img_files)}")
        print(f"  标签: {len(label_files)}")
        print(f"  总标注: {total_annotations}")
        
        if class_counts:
            print(f"  类别分布:")
            for class_id, count in sorted(class_counts.items()):
                class_name = class_names.get(class_id, f"Class_{class_id}")
                print(f"    {class_name}: {count}")

def validate_dataset(dataset_dir: str):
    """验证数据集完整性"""
    from pathlib import Path
    
    logger = logging.getLogger(__name__)
    dataset_path = Path(dataset_dir)
    
    print(f"\n📋 验证数据集: {dataset_path}")
    
    issues = []
    
    # 检查目录结构
    required_dirs = [
        "images/train", "images/val", "images/test",
        "labels/train", "labels/val", "labels/test"
    ]
    
    for dir_name in required_dirs:
        dir_path = dataset_path / dir_name
        if not dir_path.exists():
            issues.append(f"缺少目录: {dir_path}")
    
    # 检查配置文件
    yaml_files = list(dataset_path.glob("*.yaml"))
    if not yaml_files:
        issues.append("缺少YAML配置文件")
    
    # 检查图像和标签匹配
    for split in ["train", "val", "test"]:
        img_dir = dataset_path / "images" / split
        label_dir = dataset_path / "labels" / split
        
        if img_dir.exists() and label_dir.exists():
            img_files = {f.stem for f in img_dir.glob("*.png")} | {f.stem for f in img_dir.glob("*.jpg")}
            label_files = {f.stem for f in label_dir.glob("*.txt")}
            
            missing_labels = img_files - label_files
            missing_images = label_files - img_files
            
            if missing_labels:
                issues.append(f"{split} 分割中缺少标签文件: {len(missing_labels)} 个")
            
            if missing_images:
                issues.append(f"{split} 分割中缺少图像文件: {len(missing_images)} 个")
    
    # 打印结果
    if issues:
        print("❌ 发现问题:")
        for issue in issues:
            print(f"  • {issue}")
    else:
        print("✅ 数据集验证通过")
    
    return len(issues) == 0

def main():
    """主函数"""
    args = parse_args()
    
    if not args.command:
        print("请指定命令。使用 --help 查看可用命令。")
        return 1
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger("prepare_data", level=log_level)
    
    try:
        if args.command == "convert":
            logger.info(f"🔄 转换数据集: {args.source} -> {args.output}")
            
            if args.format == "unity":
                convert_unity_to_yolo(
                    source_dir=args.source,
                    output_dir=args.output,
                    train_ratio=args.train_ratio,
                    val_ratio=args.val_ratio
                )
            else:
                logger.error(f"不支持的数据格式: {args.format}")
                return 1
                
        elif args.command == "visualize":
            logger.info(f"🖼️  可视化数据集: {args.dataset}")
            visualize_dataset(
                dataset_dir=args.dataset,
                split=args.split,
                samples=args.samples,
                save_path=args.save
            )
            
        elif args.command == "stats":
            logger.info(f"📊 数据集统计: {args.dataset}")
            show_dataset_stats(args.dataset)
            
        elif args.command == "validate":
            logger.info(f"✅ 验证数据集: {args.dataset}")
            is_valid = validate_dataset(args.dataset)
            return 0 if is_valid else 1
        
        logger.info("🎉 操作完成！")
        return 0
        
    except Exception as e:
        logger.error(f"❌ 操作失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())