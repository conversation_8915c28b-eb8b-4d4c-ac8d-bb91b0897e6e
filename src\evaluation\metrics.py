"""
评估指标计算模块
"""

from typing import Dict, List, Any
import numpy as np

class MetricsCalculator:
    """评估指标计算器"""
    
    @staticmethod
    def calculate_ap(precisions: List[float], recalls: List[float]) -> float:
        """计算平均精度(AP)"""
        # 简化的AP计算
        if not precisions or not recalls:
            return 0.0
        
        # 确保精度和召回率数组长度相同
        min_len = min(len(precisions), len(recalls))
        p_array = np.array(precisions[:min_len])
        r_array = np.array(recalls[:min_len])
        
        # 计算曲线下面积
        if len(p_array) < 2:
            return 0.0
        
        # 简单的梯形积分
        ap = np.trapz(p_array, r_array)
        return float(ap)
    
    @staticmethod
    def calculate_f1(precision: float, recall: float) -> float:
        """计算F1分数"""
        if precision + recall == 0:
            return 0.0
        return 2 * precision * recall / (precision + recall)
    
    @staticmethod
    def calculate_iou(box1: List[float], box2: List[float]) -> float:
        """计算两个边界框的IoU"""
        # box格式: [x1, y1, x2, y2]
        x1_inter = max(box1[0], box2[0])
        y1_inter = max(box1[1], box2[1])
        x2_inter = min(box1[2], box2[2])
        y2_inter = min(box1[3], box2[3])
        
        if x2_inter <= x1_inter or y2_inter <= y1_inter:
            return 0.0
        
        inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
        
        box1_area = (box1[2] - box1[0]) * (box1[3] - box1[1])
        box2_area = (box2[2] - box2[0]) * (box2[3] - box2[1])
        
        union_area = box1_area + box2_area - inter_area
        
        if union_area == 0:
            return 0.0
        
        return inter_area / union_area