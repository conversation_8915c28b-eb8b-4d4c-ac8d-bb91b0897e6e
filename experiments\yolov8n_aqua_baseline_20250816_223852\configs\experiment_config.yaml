dataset:
  class_names:
    0: Observation Satellite - Aqua
  config_file: aqua_dataset.yaml
  description: Aqua观测卫星目标检测数据集
  image_size: 640
  name: aqua
  num_classes: 1
  path: datasets/Aqua_YOLO
  test_split: test
  train_split: train
  val_split: val
description: 训练yolov8n模型在aqua数据集上
device: auto
evaluation:
  conf_threshold: 0.5
  iou_threshold: 0.7
  max_det: 300
  plots: true
  save_hybrid: false
  save_json: true
model:
  name: yolov8n
  num_classes: 1
  pretrained: true
  variant: n
  version: v8
name: yolov8n_aqua_baseline
output_dir: experiments
seed: 42
training:
  amp: true
  batch_size: 16
  cache: false
  close_mosaic: 10
  copy_paste: 0.1
  degrees: 0.0
  epochs: 100
  fliplr: 0.5
  flipud: 0.0
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  learning_rate: 0.01
  mixup: 0.0
  momentum: 0.937
  mosaic: 1.0
  optimizer: AdamW
  patience: 50
  perspective: 0.0
  save_period: 10
  scale: 0.5
  scheduler: cosine
  seed: 42
  shear: 0.0
  translate: 0.1
  warmup_epochs: 3
  weight_decay: 0.0005
  workers: 4
