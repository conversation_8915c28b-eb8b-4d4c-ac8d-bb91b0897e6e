"""
模型相关测试
"""

import pytest
import sys
from pathlib import Path

# 添加src到路径
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.models import YOLOModelFactory


class TestYOLOModelFactory:
    """测试YOLO模型工厂"""
    
    def test_supported_models(self):
        """测试获取支持的模型"""
        supported = YOLOModelFactory.get_supported_models()
        
        assert "versions" in supported
        assert "variants" in supported
        assert "combinations" in supported
        
        assert "v8" in supported["versions"]
        assert "v11" in supported["versions"]
        assert "v13" in supported["versions"]
        
        assert "n" in supported["variants"]
        assert "s" in supported["variants"]
    
    def test_model_name_validation(self):
        """测试模型名称验证"""
        # 有效的模型名称
        valid_names = ["yolov8n", "yolov8s", "yolo11s", "yolov13x"]
        for name in valid_names:
            assert YOLOModelFactory.validate_model_name(name)
        
        # 无效的模型名称
        invalid_names = ["yolov7n", "yolo8", "invalid", "yolov8z"]
        for name in invalid_names:
            assert not YOLOModelFactory.validate_model_name(name)
    
    def test_model_name_parsing(self):
        """测试模型名称解析"""
        test_cases = [
            ("yolov8n", {"version": "v8", "variant": "n"}),
            ("yolo11s", {"version": "11", "variant": "s"}),
            ("yolov13x", {"version": "v13", "variant": "x"}),
        ]
        
        for name, expected in test_cases:
            parsed = YOLOModelFactory.parse_model_name(name)
            assert parsed["version"] == expected["version"]
            assert parsed["variant"] == expected["variant"]
            assert parsed["full_name"] == name
    
    def test_create_model_config(self):
        """测试通过配置创建模型"""
        config = {
            "version": "v8",
            "variant": "n",
            "num_classes": 1,
            "pretrained": True
        }
        
        model = YOLOModelFactory.create_from_config(config)
        
        model_info = model.get_model_info()
        assert model_info["version"] == "v8"
        assert model_info["variant"] == "n"
        assert model_info["num_classes"] == 1