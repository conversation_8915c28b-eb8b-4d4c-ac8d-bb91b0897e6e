# 使用教程

## 快速开始

### 1. 数据准备

#### 转换Aqua数据集

```bash
# 查看数据准备帮助
python scripts/prepare_data.py --help

# 转换Unity Solo格式到YOLO格式
python scripts/prepare_data.py convert \
  --source datasets/Aqua/1-RGB-200张 \
  --output datasets/Aqua_YOLO \
  --format unity \
  --train-ratio 0.7 \
  --val-ratio 0.2

# 验证数据集
python scripts/prepare_data.py validate \
  --dataset datasets/Aqua_YOLO

# 查看数据集统计
python scripts/prepare_data.py stats \
  --dataset datasets/Aqua_YOLO

# 可视化数据集
python scripts/prepare_data.py visualize \
  --dataset datasets/Aqua_YOLO \
  --split train \
  --samples 4
```

### 2. 模型训练

#### 基础训练

```bash
# 训练YOLOv8n模型
python scripts/train.py \
  --model yolov8n \
  --dataset aqua \
  --experiment yolov8n_baseline \
  --epochs 100 \
  --batch-size 16

# 训练YOLOv11s模型
python scripts/train.py \
  --model yolo11s \
  --dataset aqua \
  --experiment yolo11s_baseline \
  --epochs 100

# 训练YOLOv13x模型
python scripts/train.py \
  --model yolov13x \
  --dataset aqua \
  --experiment yolov13x_baseline \
  --epochs 100
```

#### 高级训练选项

```bash
# 使用自定义配置
python scripts/train.py \
  --model yolov8n \
  --dataset aqua \
  --config yolov8_config \
  --experiment custom_experiment \
  --epochs 200 \
  --lr 0.005 \
  --device 0

# 从检查点恢复训练
python scripts/train.py \
  --model yolov8n \
  --dataset aqua \
  --experiment resume_training \
  --resume experiments/yolov8n_baseline/weights/last.pt
```

### 3. 模型评估

#### 单模型评估

```bash
# 评估训练好的模型
python scripts/evaluate.py \
  --model experiments/yolov8n_baseline/weights/best.pt \
  --dataset aqua \
  --experiment eval_yolov8n

# 调整评估参数
python scripts/evaluate.py \
  --model experiments/yolov8n_baseline/weights/best.pt \
  --dataset aqua \
  --conf 0.3 \
  --iou 0.5 \
  --split test
```

#### 批量评估

```bash
# 批量评估多个模型
python scripts/evaluate.py \
  --models \
    experiments/yolov8n_baseline/weights/best.pt \
    experiments/yolo11s_baseline/weights/best.pt \
    experiments/yolov13x_baseline/weights/best.pt \
  --dataset aqua \
  --experiment batch_evaluation
```

#### 从实验目录评估

```bash
# 从实验目录自动加载最佳模型
python scripts/evaluate.py \
  --experiments \
    experiments/yolov8n_baseline \
    experiments/yolo11s_baseline \
    experiments/yolov13x_baseline \
  --dataset aqua \
  --experiment experiment_comparison
```

### 4. 模型比较

#### 比较评估结果

```bash
# 比较已有的评估结果
python scripts/compare_models.py \
  --evaluations \
    eval_yolov8n \
    eval_yolo11s \
    eval_yolov13x \
  --output final_comparison

# 指定比较指标
python scripts/compare_models.py \
  --evaluations eval_yolov8n eval_yolo11s \
  --metrics mAP50-95 mAP50 precision recall \
  --output custom_comparison \
  --save-plot comparison_chart.png
```

## 配置管理

### 数据集配置

编辑 `configs/datasets/aqua.yaml`:

```yaml
name: aqua
path: datasets/Aqua_YOLO
config_file: aqua_dataset.yaml
num_classes: 1
class_names:
  0: "Observation Satellite - Aqua"
image_size: 640
```

### 训练配置

编辑 `configs/training/custom_config.yaml`:

```yaml
epochs: 150
batch_size: 32
learning_rate: 0.001
optimizer: "AdamW"

# 数据增强
degrees: 0.0
fliplr: 0.5
mosaic: 1.0
mixup: 0.1
```

### 模型配置

创建自定义模型配置 `configs/models/yolov8/custom.yaml`:

```yaml
name: "custom_yolov8n"
version: "v8"
variant: "n"
pretrained: true
num_classes: 1
```

## 实验管理

### 实验目录结构

```
experiments/
├── yolov8n_baseline_20250116_143022/
│   ├── checkpoints/          # 训练检查点
│   ├── configs/             # 实验配置
│   ├── logs/                # 训练日志
│   ├── results/             # 训练结果
│   └── visualizations/      # 可视化图表
└── evaluations/
    ├── eval_yolov8n_20250116_143500/
    └── final_comparison_20250116_144000/
```

### 查看实验结果

```bash
# 查看训练日志
cat experiments/yolov8n_baseline_*/logs/*.log

# 查看训练结果
cat experiments/yolov8n_baseline_*/results/training_results.json

# 查看评估结果
cat experiments/evaluations/eval_yolov8n_*/evaluation_results.json
```

## 高级用法

### 自定义数据集

1. 创建数据集配置文件 `configs/datasets/custom.yaml`
2. 准备YOLO格式的数据集
3. 使用新的数据集配置进行训练

### 添加新的YOLO版本

1. 在 `src/models/` 下创建新的模型文件
2. 继承 `BaseYOLOModel` 类
3. 在 `YOLOModelFactory` 中注册新模型

### 性能优化

- 使用更大的batch_size (如果GPU内存允许)
- 启用混合精度训练 (amp=True)
- 使用多GPU训练 (device="0,1,2,3")
- 调整数据加载器参数 (workers)

## 故障排除

### 训练问题

1. **GPU内存不足**: 减少batch_size或image_size
2. **训练速度慢**: 增加workers数量，启用amp
3. **模型不收敛**: 调整学习率，检查数据质量

### 评估问题

1. **精度较低**: 检查数据标注质量，调整置信度阈值
2. **评估失败**: 确保模型路径正确，数据集格式正确

更多详细信息请参考 [API文档](../api_reference.md) 和 [故障排除指南](../troubleshooting.md)。