"""
统一训练器
提供多YOLO模型的统一训练接口
"""

from typing import Dict, Any, Optional
from pathlib import Path
import logging

from ..models.base_model import BaseYOLOModel
from ..utils.helpers import create_experiment_dir, save_checkpoint

logger = logging.getLogger(__name__)

class UnifiedTrainer:
    """统一模型训练器"""
    
    def __init__(self, output_dir: str = "experiments"):
        """
        初始化训练器
        
        Args:
            output_dir: 训练结果输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def train_model(self,
                   model: BaseYOLOModel,
                   dataset_config: Dict[str, Any],
                   training_config: Dict[str, Any],
                   experiment_name: str) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            model: YOLO模型实例
            dataset_config: 数据集配置
            training_config: 训练配置
            experiment_name: 实验名称
            
        Returns:
            训练结果字典
        """
        logger.info(f"🏋️  开始训练模型: {model}")
        
        # 创建实验目录
        exp_dir = create_experiment_dir(experiment_name, str(self.output_dir))
        
        try:
            # 执行训练
            train_results = model.train(
                dataset_config=dataset_config,
                training_config=training_config,
                output_dir=str(exp_dir)
            )
            
            # 保存训练配置和结果
            config_path = exp_dir / "configs" / "training_config.json"
            save_checkpoint(training_config, str(config_path), format="json")
            
            results_path = exp_dir / "results" / "training_results.json"
            save_checkpoint(train_results, str(results_path), format="json")
            
            logger.info(f"✅ 模型训练完成: {model}")
            return train_results
            
        except Exception as e:
            logger.error(f"❌ 模型训练失败: {e}")
            raise