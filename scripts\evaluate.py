#!/usr/bin/env python3
"""
统一的多YOLO模型评估脚本

支持YOLOv8、YOLOv11、YOLOv13的评估和比较
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils import ConfigManager, setup_logger, get_device, set_seed
from src.models import YOLOModelFactory
from src.evaluation import UnifiedEvaluator
import logging

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="多YOLO模型评估脚本")
    
    # 评估模式
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--model", type=str,
                      help="单个模型评估，指定模型权重路径")
    group.add_argument("--models", type=str, nargs="+",
                      help="多模型评估，指定多个模型权重路径")
    group.add_argument("--experiments", type=str, nargs="+",
                      help="从实验结果中评估，指定实验目录")
    group.add_argument("--compare", type=str, nargs="+",
                      help="比较已有的评估结果，指定评估实验名称")
    
    # 基本参数
    parser.add_argument("--dataset", type=str, default="aqua",
                       help="数据集名称")
    parser.add_argument("--experiment", type=str, required=False,
                       help="评估实验名称")
    
    # 评估参数
    parser.add_argument("--conf", type=float, default=0.5,
                       help="置信度阈值")
    parser.add_argument("--iou", type=float, default=0.7,
                       help="IoU阈值")
    parser.add_argument("--split", type=str, default="test",
                       choices=["train", "val", "test"],
                       help="评估数据集分割")
    parser.add_argument("--save-json", action="store_true",
                       help="保存JSON格式结果")
    parser.add_argument("--plots", action="store_true", default=True,
                       help="生成评估图表")
    
    # 其他选项
    parser.add_argument("--device", type=str, default="auto",
                       help="设备 (auto, cpu, gpu, 0, 1, etc.)")
    parser.add_argument("--verbose", action="store_true",
                       help="详细输出")
    
    return parser.parse_args()

def load_model_from_path(model_path: str):
    """从模型路径加载模型"""
    model_path = Path(model_path)
    
    if not model_path.exists():
        raise FileNotFoundError(f"模型文件不存在: {model_path}")
    
    # 从完整路径推断模型类型，不仅仅是文件名
    full_path = str(model_path).lower()
    model_name = model_path.stem
    
    # 解析模型名称 - 检查完整路径
    if "yolov13" in full_path or "yolo13" in full_path:
        version = "v13"
    elif "yolov8" in full_path or "yolo8" in full_path:
        version = "v8"
    elif "yolo11" in full_path or "yolov11" in full_path:
        version = "v11"
    else:
        # 默认使用v8
        version = "v8"
        logging.warning(f"无法从路径推断模型版本，使用默认版本: {version}")
    
    # 推断变体 - 从路径中查找
    variant = "n"  # 默认
    for v in ["n", "s", "m", "l", "x"]:
        if f"yolo{version[1:]}{v}" in full_path or f"yolov{version[1:]}{v}" in full_path:
            variant = v
            break
    
    # 创建模型
    model = YOLOModelFactory.create_model(
        version=version,
        variant=variant,
        weights=str(model_path)
    )
    
    return model

def load_model_from_experiment(exp_dir: str):
    """从实验目录加载最佳模型"""
    exp_path = Path(exp_dir)
    
    # 检查新的目录结构
    weight_paths = [
        exp_path / "weights" / "best.pt",  # 标准结构：weights目录
    ]
    
    # 检查旧的目录结构（兼容性）
    for subdir in exp_path.iterdir():
        if subdir.is_dir() and subdir.name.startswith(("yolo", "model")):
            weight_paths.append(subdir / "weights" / "best.pt")
    
    for weight_path in weight_paths:
        if weight_path.exists():
            return load_model_from_path(str(weight_path))
    
    raise FileNotFoundError(f"在实验目录中找不到模型权重: {exp_dir}")
    
    if not exp_path.exists():
        raise FileNotFoundError(f"实验目录不存在: {exp_path}")
    
    # 查找最佳权重文件
    weights_dir = exp_path / "weights"
    if weights_dir.exists():
        best_weights = weights_dir / "best.pt"
        if best_weights.exists():
            return load_model_from_path(str(best_weights))
    
    # 查找其他可能的权重文件
    for weights_file in exp_path.rglob("*.pt"):
        if "best" in weights_file.name:
            return load_model_from_path(str(weights_file))
    
    raise FileNotFoundError(f"在实验目录中找不到模型权重文件: {exp_path}")

def main():
    """主函数"""
    args = parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger("evaluate", level=log_level)
    
    logger.info("📊 启动多YOLO模型评估系统")
    
    try:
        # 获取设备
        device = get_device(args.device)
        
        # 初始化配置管理器和评估器
        config_manager = ConfigManager()
        evaluator = UnifiedEvaluator()
        
        # 加载数据集配置
        try:
            dataset_config = config_manager.load_dataset_config(args.dataset)
            logger.info(f"✅ 加载数据集配置: {args.dataset}")
        except Exception as e:
            logger.error(f"❌ 加载数据集配置失败: {e}")
            return 1
        
        # 准备评估配置
        eval_config = {
            "split": args.split,
            "conf_threshold": args.conf,
            "iou_threshold": args.iou,
            "save_json": args.save_json,
            "plots": args.plots
        }
        
        dataset_config_dict = {
            "name": dataset_config.name,
            "path": dataset_config.path,
            "config_file": dataset_config.config_file,
            "image_size": dataset_config.image_size,
            "num_classes": dataset_config.num_classes
        }
        
        # 根据不同模式执行评估
        if args.model:
            # 单模型评估
            logger.info(f"📊 单模型评估模式: {args.model}")
            
            model = load_model_from_path(args.model)
            
            experiment_name = args.experiment or f"eval_{model.get_model_info()['name']}"
            
            result = evaluator.evaluate_model(
                model=model,
                dataset_config=dataset_config_dict,
                eval_config=eval_config,
                experiment_name=experiment_name
            )
            
            # 打印结果
            print(f"\n📊 评估结果 - {result['model_name']}:")
            print(f"   mAP50-95: {result.get('mAP50-95', 'N/A'):.4f}")
            print(f"   mAP50:    {result.get('mAP50', 'N/A'):.4f}")
            print(f"   mAP75:    {result.get('mAP75', 'N/A'):.4f}")
            print(f"   Precision: {result.get('precision', 'N/A'):.4f}")
            print(f"   Recall:    {result.get('recall', 'N/A'):.4f}")
            print(f"   F1-Score:  {result.get('f1_score', 'N/A'):.4f}")
            
        elif args.models:
            # 多模型评估
            logger.info(f"📊 多模型评估模式: {len(args.models)} 个模型")
            
            models = []
            for model_path in args.models:
                try:
                    model = load_model_from_path(model_path)
                    models.append(model)
                except Exception as e:
                    logger.error(f"❌ 加载模型失败 {model_path}: {e}")
            
            if not models:
                logger.error("❌ 没有成功加载任何模型")
                return 1
            
            experiment_name = args.experiment or "batch_evaluation"
            
            batch_results = evaluator.batch_evaluate(
                models=models,
                dataset_config=dataset_config_dict,
                eval_config=eval_config,
                experiment_name=experiment_name
            )
            
            # 自动生成比较
            successful_results = []
            for model_name, result in batch_results["results"].items():
                if isinstance(result, dict) and "status" not in result:
                    successful_results.append(result)
            
            if len(successful_results) > 1:
                comparison = evaluator.compare_models(
                    eval_results=successful_results,
                    experiment_name=f"{experiment_name}_comparison"
                )
                
                # 打印比较结果
                print(f"\n🏆 模型比较结果:")
                for metric in ["mAP50-95", "mAP50", "precision", "recall"]:
                    if metric in comparison["best_models"]:
                        best = comparison["best_models"][metric]
                        print(f"   {metric:10s}: {best['model']:15s} ({best['value']:.4f})")
            
        elif args.experiments:
            # 从实验结果评估
            logger.info(f"📊 实验结果评估模式: {len(args.experiments)} 个实验")
            
            models = []
            for exp_dir in args.experiments:
                try:
                    model = load_model_from_experiment(exp_dir)
                    models.append(model)
                except Exception as e:
                    logger.error(f"❌ 从实验加载模型失败 {exp_dir}: {e}")
            
            if not models:
                logger.error("❌ 没有成功加载任何模型")
                return 1
            
            experiment_name = args.experiment or "experiment_evaluation"
            
            batch_results = evaluator.batch_evaluate(
                models=models,
                dataset_config=dataset_config_dict,
                eval_config=eval_config,
                experiment_name=experiment_name
            )
            
        elif args.compare:
            # 比较已有评估结果
            logger.info(f"📊 比较模式: {len(args.compare)} 个评估结果")
            
            eval_results = []
            for eval_name in args.compare:
                result = evaluator.load_evaluation_results(eval_name)
                if result:
                    eval_results.append(result)
                else:
                    logger.warning(f"⚠️  无法加载评估结果: {eval_name}")
            
            if not eval_results:
                logger.error("❌ 没有成功加载任何评估结果")
                return 1
            
            experiment_name = args.experiment or "comparison_analysis"
            
            comparison = evaluator.compare_models(
                eval_results=eval_results,
                experiment_name=experiment_name
            )
            
            # 打印详细比较结果
            print(f"\n📊 模型比较分析:")
            print(f"   比较模型数量: {len(eval_results)}")
            
            if comparison["best_models"]:
                print(f"\n🏆 各指标最佳模型:")
                for metric, best in comparison["best_models"].items():
                    print(f"   {metric:10s}: {best['model']:15s} ({best['value']:.4f})")
            
            if comparison["analysis"]["overall_best"]:
                overall = comparison["analysis"]["overall_best"]
                print(f"\n👑 综合最佳模型: {overall['model']} (得分: {overall['score']:.4f})")
            
            if comparison["analysis"]["recommendations"]:
                print(f"\n💡 建议:")
                for rec in comparison["analysis"]["recommendations"]:
                    print(f"   • {rec}")
        
        logger.info("🎉 评估完成！")
        return 0
        
    except KeyboardInterrupt:
        logger.info("⏹️  评估被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 评估过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())