"""
YOLO模型基类
定义所有YOLO模型的统一接口
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class BaseYOLOModel(ABC):
    """YOLO模型基类"""
    
    def __init__(self, 
                 model_config: Dict[str, Any],
                 weights: Optional[str] = None):
        """
        初始化模型
        
        Args:
            model_config: 模型配置
            weights: 预训练权重路径
        """
        self.model_config = model_config
        self.weights = weights
        self.model = None
        self.is_trained = False
        
        # 模型信息
        self.version = model_config.get("version", "unknown")
        self.variant = model_config.get("variant", "n")
        self.num_classes = model_config.get("num_classes", 1)
        
        logger.info(f"初始化 {self.__class__.__name__}: {self.version}{self.variant}")
    
    @abstractmethod
    def load_model(self, weights: Optional[str] = None) -> bool:
        """
        加载模型
        
        Args:
            weights: 权重文件路径
            
        Returns:
            是否加载成功
        """
        pass
    
    @abstractmethod
    def train(self, 
              dataset_config: Dict[str, Any],
              training_config: Dict[str, Any],
              output_dir: str) -> Dict[str, Any]:
        """
        训练模型
        
        Args:
            dataset_config: 数据集配置
            training_config: 训练配置
            output_dir: 输出目录
            
        Returns:
            训练结果
        """
        pass
    
    @abstractmethod
    def evaluate(self,
                dataset_config: Dict[str, Any], 
                eval_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        评估模型
        
        Args:
            dataset_config: 数据集配置
            eval_config: 评估配置
            
        Returns:
            评估结果
        """
        pass
    
    @abstractmethod
    def predict(self, 
                source: str,
                conf_threshold: float = 0.5,
                iou_threshold: float = 0.7,
                save_results: bool = False) -> List[Dict[str, Any]]:
        """
        预测
        
        Args:
            source: 输入源（图片路径、视频路径或目录）
            conf_threshold: 置信度阈值
            iou_threshold: IoU阈值
            save_results: 是否保存结果
            
        Returns:
            预测结果列表
        """
        pass
    
    @abstractmethod
    def export(self, 
               format: str = "onnx",
               optimize: bool = True,
               half: bool = False) -> str:
        """
        导出模型
        
        Args:
            format: 导出格式 (onnx, engine, etc.)
            optimize: 是否优化
            half: 是否使用半精度
            
        Returns:
            导出文件路径
        """
        pass
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        info = {
            "name": f"YOLOv{self.version}{self.variant}",
            "version": self.version,
            "variant": self.variant,
            "num_classes": self.num_classes,
            "is_trained": self.is_trained,
            "weights": self.weights
        }
        
        if self.model is not None:
            try:
                # 尝试获取模型参数数量
                from ..utils.helpers import count_parameters
                params = count_parameters(self.model.model if hasattr(self.model, 'model') else self.model)
                info.update(params)
            except:
                pass
        
        return info
    
    def save_weights(self, save_path: str) -> bool:
        """
        保存模型权重
        
        Args:
            save_path: 保存路径
            
        Returns:
            是否保存成功
        """
        if self.model is None:
            logger.error("模型未加载，无法保存权重")
            return False
        
        try:
            # 不同YOLO版本可能有不同的保存方法
            save_path = Path(save_path)
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            if hasattr(self.model, 'save'):
                self.model.save(str(save_path))
            else:
                # 使用torch.save作为后备方案
                import torch
                torch.save(self.model.state_dict(), save_path)
            
            logger.info(f"模型权重已保存: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存模型权重失败: {e}")
            return False
    
    def load_weights(self, weights_path: str) -> bool:
        """
        加载模型权重
        
        Args:
            weights_path: 权重文件路径
            
        Returns:
            是否加载成功
        """
        if not Path(weights_path).exists():
            logger.error(f"权重文件不存在: {weights_path}")
            return False
        
        try:
            self.weights = weights_path
            return self.load_model(weights_path)
        except Exception as e:
            logger.error(f"加载模型权重失败: {e}")
            return False
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"YOLOv{self.version}{self.variant}(classes={self.num_classes}, trained={self.is_trained})"
    
    def __repr__(self) -> str:
        """对象表示"""
        return self.__str__()