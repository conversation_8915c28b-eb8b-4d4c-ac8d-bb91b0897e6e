"""
YOLOv13模型实现
"""

from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

from .base_model import BaseYOLOModel

logger = logging.getLogger(__name__)

class YOLOv13Model(BaseYOLOModel):
    """YOLOv13模型实现"""
    
    def __init__(self, model_config: Dict[str, Any], weights: Optional[str] = None):
        """初始化YOLOv13模型"""
        super().__init__(model_config, weights)
        self.model_name = f"yolov13{self.variant}.pt"
        
    def load_model(self, weights: Optional[str] = None) -> bool:
        """加载YOLOv13模型"""
        try:
            from ultralytics import YOLO
            
            if weights:
                self.weights = weights
            
            if self.weights and Path(self.weights).exists():
                # 加载自定义权重
                self.model = YOLO(self.weights)
                logger.info(f"加载自定义权重: {self.weights}")
            else:
                # 加载预训练模型
                if self.model_config.get("pretrained", True):
                    # 尝试加载预训练的YOLOv13模型
                    try:
                        self.model = YOLO(self.model_name)
                        logger.info(f"加载预训练模型: {self.model_name}")
                    except:
                        # 如果预训练模型不可用，从配置文件创建
                        config_path = "ultralytics/cfg/models/v13/yolov13.yaml"
                        self.model = YOLO(config_path)
                        logger.info(f"从配置创建模型: {config_path}")
                else:
                    # 从配置文件创建模型
                    config_path = "ultralytics/cfg/models/v13/yolov13.yaml"
                    self.model = YOLO(config_path)
                    logger.info(f"从配置创建模型: {config_path}")
            
            # 设置类别数和模型规模
            if hasattr(self.model.model, 'nc'):
                self.model.model.nc = self.num_classes
            
            # 为YOLOv13设置特定的规模参数
            if hasattr(self.model.model, 'yaml'):
                scales = {
                    'n': [0.50, 0.25, 1024],   # Nano
                    's': [0.50, 0.50, 1024],   # Small  
                    'm': [0.75, 0.75, 768],    # Medium
                    'l': [1.00, 1.00, 512],    # Large
                    'x': [1.00, 1.50, 512]     # Extra Large
                }
                if self.variant in scales:
                    self.model.model.yaml['scales'] = {self.variant: scales[self.variant]}
            
            return True
            
        except Exception as e:
            logger.error(f"加载YOLOv13模型失败: {e}")
            return False
    
    def train(self, 
              dataset_config: Dict[str, Any],
              training_config: Dict[str, Any], 
              output_dir: str) -> Dict[str, Any]:
        """训练YOLOv13模型"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            # 准备训练参数 - YOLOv13专门优化的参数
            train_args = {
                "data": dataset_config["path"] + "/" + dataset_config.get("config_file", "dataset.yaml"),
                "epochs": training_config.get("epochs", 100),
                "batch": training_config.get("batch_size", 16),
                "imgsz": dataset_config.get("image_size", 640),
                "device": training_config.get("device", "auto"),
                "project": str(Path(output_dir).parent),
                "name": Path(output_dir).name,
                
                # 优化器设置 - 针对YOLOv13优化
                "optimizer": training_config.get("optimizer", "AdamW"),
                "lr0": training_config.get("learning_rate", 0.01),
                "lrf": 0.01,
                "momentum": training_config.get("momentum", 0.937),
                "weight_decay": training_config.get("weight_decay", 0.0005),
                "warmup_epochs": training_config.get("warmup_epochs", 3),
                "warmup_momentum": 0.8,
                
                # 数据增强 - 卫星图像专门优化
                "hsv_h": training_config.get("hsv_h", 0.015),
                "hsv_s": training_config.get("hsv_s", 0.7),
                "hsv_v": training_config.get("hsv_v", 0.4),
                "degrees": training_config.get("degrees", 0.0),  # 卫星通常不旋转
                "translate": training_config.get("translate", 0.1),
                "scale": training_config.get("scale", 0.5),
                "shear": training_config.get("shear", 0.0),
                "perspective": training_config.get("perspective", 0.0),
                "flipud": training_config.get("flipud", 0.0),
                "fliplr": training_config.get("fliplr", 0.5),
                "mosaic": training_config.get("mosaic", 1.0),
                "mixup": training_config.get("mixup", 0.0),
                "copy_paste": training_config.get("copy_paste", 0.1),
                
                # YOLOv13特有的训练设置
                "amp": training_config.get("amp", True),
                "cache": training_config.get("cache", False),
                "workers": training_config.get("workers", 4),
                "save_period": training_config.get("save_period", 10),
                "patience": training_config.get("patience", 50),
                "close_mosaic": training_config.get("close_mosaic", 10),
                "seed": training_config.get("seed", 42),
                "deterministic": True,
                "val": True,
                "save": True,
                "plots": True,
                "verbose": True
            }
            
            # 根据模型变体调整特定参数
            variant_specific = {
                'n': {"lr0": 0.01, "mixup": 0.0},
                's': {"lr0": 0.01, "mixup": 0.05},
                'm': {"lr0": 0.01, "mixup": 0.10},
                'l': {"lr0": 0.01, "mixup": 0.15},
                'x': {"lr0": 0.01, "mixup": 0.20}
            }
            
            if self.variant in variant_specific:
                train_args.update(variant_specific[self.variant])
            
            logger.info("开始训练YOLOv13模型...")
            results = self.model.train(**train_args)
            
            self.is_trained = True
            
            # 提取训练结果
            train_results = {
                "model_name": f"yolov13{self.variant}",
                "final_epoch": train_args["epochs"],
                "best_fitness": float(results.best_fitness) if hasattr(results, 'best_fitness') else None,
                "results_dict": results.results_dict if hasattr(results, 'results_dict') else {},
                "save_dir": str(results.save_dir) if hasattr(results, 'save_dir') else output_dir
            }
            
            logger.info("YOLOv13训练完成")
            return train_results
            
        except Exception as e:
            logger.error(f"YOLOv13训练失败: {e}")
            raise
    
    def evaluate(self,
                dataset_config: Dict[str, Any],
                eval_config: Dict[str, Any]) -> Dict[str, Any]:
        """评估YOLOv13模型"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            # 准备评估参数
            val_args = {
                "data": dataset_config["path"] + "/" + dataset_config.get("config_file", "dataset.yaml"),
                "split": eval_config.get("split", "test"),
                "conf": eval_config.get("conf_threshold", 0.5),
                "iou": eval_config.get("iou_threshold", 0.7),
                "max_det": eval_config.get("max_det", 300),
                "save_json": eval_config.get("save_json", True),
                "save_hybrid": eval_config.get("save_hybrid", False),
                "plots": eval_config.get("plots", True),
                "verbose": True
            }
            
            logger.info("开始评估YOLOv13模型...")
            results = self.model.val(**val_args)
            
            # 提取评估结果
            eval_results = {
                "model_name": f"yolov13{self.variant}",
                "mAP50-95": float(results.box.map),
                "mAP50": float(results.box.map50),
                "mAP75": float(results.box.map75),
                "precision": float(results.box.mp),
                "recall": float(results.box.mr),
                "f1_score": 2 * float(results.box.mp) * float(results.box.mr) / (float(results.box.mp) + float(results.box.mr)) if (float(results.box.mp) + float(results.box.mr)) > 0 else 0,
                "results_dict": results.results_dict if hasattr(results, 'results_dict') else {}
            }
            
            logger.info("YOLOv13评估完成")
            return eval_results
            
        except Exception as e:
            logger.error(f"YOLOv13评估失败: {e}")
            raise
    
    def predict(self, 
                source: str,
                conf_threshold: float = 0.5,
                iou_threshold: float = 0.7,
                save_results: bool = False) -> List[Dict[str, Any]]:
        """YOLOv13预测"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            results = self.model.predict(
                source=source,
                conf=conf_threshold,
                iou=iou_threshold,
                save=save_results,
                verbose=False
            )
            
            # 转换结果格式
            predictions = []
            for result in results:
                pred = {
                    "image_path": result.path,
                    "image_shape": result.orig_shape,
                    "boxes": [],
                    "num_detections": len(result.boxes) if result.boxes is not None else 0
                }
                
                if result.boxes is not None:
                    for box in result.boxes:
                        box_info = {
                            "xyxy": box.xyxy[0].tolist(),
                            "conf": float(box.conf[0]),
                            "cls": int(box.cls[0]),
                            "class_name": self.model.names[int(box.cls[0])] if hasattr(self.model, 'names') else f"class_{int(box.cls[0])}"
                        }
                        pred["boxes"].append(box_info)
                
                predictions.append(pred)
            
            return predictions
            
        except Exception as e:
            logger.error(f"YOLOv13预测失败: {e}")
            raise
    
    def export(self, 
               format: str = "onnx",
               optimize: bool = True,
               half: bool = False) -> str:
        """导出YOLOv13模型"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            exported_path = self.model.export(
                format=format,
                optimize=optimize,
                half=half
            )
            
            logger.info(f"YOLOv13模型已导出: {exported_path}")
            return str(exported_path)
            
        except Exception as e:
            logger.error(f"YOLOv13模型导出失败: {e}")
            raise