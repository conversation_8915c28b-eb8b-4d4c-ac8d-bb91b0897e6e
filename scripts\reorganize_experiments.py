#!/usr/bin/env python3
"""
重新组织实验目录结构脚本
将旧的嵌套结构重组为标准结构
"""

import argparse
import shutil
from pathlib import Path
import logging

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    return logging.getLogger(__name__)

def reorganize_experiment(exp_dir: Path, logger):
    """重新组织单个实验目录"""
    logger.info(f"📁 重新组织实验目录: {exp_dir.name}")
    
    # 查找所有可能的YOLO子目录
    yolo_subdirs = []
    for subdir in exp_dir.iterdir():
        if subdir.is_dir() and any(pattern in subdir.name.lower() for pattern in ['yolo', 'model']):
            yolo_subdirs.append(subdir)
    
    if not yolo_subdirs:
        logger.info(f"  ✅ 目录结构已经是标准格式: {exp_dir.name}")
        return
    
    # 处理每个YOLO子目录
    for yolo_dir in yolo_subdirs:
        logger.info(f"  📦 处理子目录: {yolo_dir.name}")
        
        # 移动权重文件
        weights_src = yolo_dir / "weights"
        if weights_src.exists():
            weights_dst = exp_dir / "weights"
            if not weights_dst.exists():
                logger.info(f"    📁 移动 weights: {weights_src} -> {weights_dst}")
                shutil.move(str(weights_src), str(weights_dst))
            else:
                # 如果目标目录已存在，合并内容
                logger.info(f"    🔄 合并 weights 文件到现有目录")
                for weight_file in weights_src.iterdir():
                    if not (weights_dst / weight_file.name).exists():
                        shutil.move(str(weight_file), str(weights_dst / weight_file.name))
                # 删除空的源目录
                if not list(weights_src.iterdir()):
                    weights_src.rmdir()
        
        # 移动可视化文件
        viz_files = [
            "*.png", "*.jpg", "*.jpeg", "*.pdf"
        ]
        viz_dst = exp_dir / "visualizations"
        viz_dst.mkdir(exist_ok=True)
        
        moved_files = 0
        for pattern in viz_files:
            for file in yolo_dir.glob(pattern):
                if not (viz_dst / file.name).exists():
                    shutil.move(str(file), str(viz_dst / file.name))
                    moved_files += 1
        
        if moved_files > 0:
            logger.info(f"    🖼️  移动了 {moved_files} 个可视化文件到 visualizations/")
        
        # 移动日志文件
        logs_dst = exp_dir / "logs"
        logs_dst.mkdir(exist_ok=True)
        
        log_files = list(yolo_dir.glob("*.log")) + list(yolo_dir.glob("events.out.*"))
        for log_file in log_files:
            if not (logs_dst / log_file.name).exists():
                shutil.move(str(log_file), str(logs_dst / log_file.name))
        
        if log_files:
            logger.info(f"    📝 移动了 {len(log_files)} 个日志文件到 logs/")
        
        # 移动结果文件
        results_dst = exp_dir / "results"
        results_dst.mkdir(exist_ok=True)
        
        result_files = list(yolo_dir.glob("*.csv")) + list(yolo_dir.glob("results.png"))
        for result_file in result_files:
            if not (results_dst / result_file.name).exists():
                shutil.move(str(result_file), str(results_dst / result_file.name))
        
        if result_files:
            logger.info(f"    📊 移动了 {len(result_files)} 个结果文件到 results/")
        
        # 移动配置文件
        configs_dst = exp_dir / "configs"
        configs_dst.mkdir(exist_ok=True)
        
        config_files = list(yolo_dir.glob("*.yaml")) + list(yolo_dir.glob("args.yaml"))
        for config_file in config_files:
            if not (configs_dst / config_file.name).exists():
                shutil.move(str(config_file), str(configs_dst / config_file.name))
        
        if config_files:
            logger.info(f"    ⚙️  移动了 {len(config_files)} 个配置文件到 configs/")
        
        # 检查是否还有其他文件
        remaining_files = list(yolo_dir.iterdir())
        if remaining_files:
            logger.warning(f"    ⚠️  子目录中还有 {len(remaining_files)} 个文件未处理:")
            for file in remaining_files[:5]:  # 只显示前5个
                logger.warning(f"      - {file.name}")
            if len(remaining_files) > 5:
                logger.warning(f"      ... 还有 {len(remaining_files) - 5} 个文件")
        else:
            # 删除空的子目录
            logger.info(f"    🗑️  删除空的子目录: {yolo_dir.name}")
            yolo_dir.rmdir()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="重新组织实验目录结构")
    parser.add_argument("--experiments-dir", type=str, default="experiments",
                       help="实验根目录")
    parser.add_argument("--experiment", type=str,
                       help="指定单个实验目录")
    parser.add_argument("--dry-run", action="store_true",
                       help="只显示将要执行的操作，不实际移动文件")
    
    args = parser.parse_args()
    logger = setup_logging()
    
    logger.info("🔄 启动实验目录重组脚本")
    
    experiments_dir = Path(args.experiments_dir)
    if not experiments_dir.exists():
        logger.error(f"❌ 实验目录不存在: {experiments_dir}")
        return
    
    if args.experiment:
        # 处理单个实验
        exp_dir = experiments_dir / args.experiment
        if not exp_dir.exists():
            logger.error(f"❌ 指定的实验目录不存在: {exp_dir}")
            return
        
        if args.dry_run:
            logger.info("🔍 DRY RUN 模式 - 只显示操作，不实际执行")
        
        reorganize_experiment(exp_dir, logger)
    else:
        # 处理所有实验
        experiment_dirs = [d for d in experiments_dir.iterdir() 
                          if d.is_dir() and not d.name.startswith('.')]
        
        logger.info(f"📁 找到 {len(experiment_dirs)} 个实验目录")
        
        if args.dry_run:
            logger.info("🔍 DRY RUN 模式 - 只显示操作，不实际执行")
        
        for exp_dir in experiment_dirs:
            try:
                reorganize_experiment(exp_dir, logger)
            except Exception as e:
                logger.error(f"❌ 处理实验 {exp_dir.name} 时出错: {e}")
    
    logger.info("✅ 实验目录重组完成")

if __name__ == "__main__":
    main()