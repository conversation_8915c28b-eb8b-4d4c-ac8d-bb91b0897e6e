# Legacy Scripts

这个目录包含了项目重构前的原始脚本文件。

## 文件说明

- `convert_aqua_to_yolo.py` - 原始的数据集转换脚本
- `train_aqua.py` - 原始的YOLOv13训练脚本  
- `evaluate_model.py` - 原始的模型评估脚本
- `visualize_yolo_dataset.py` - 原始的数据集可视化脚本

## 新的统一脚本

请使用 `scripts/` 目录下的新脚本：

- `scripts/prepare_data.py` - 统一的数据准备脚本
- `scripts/train.py` - 多YOLO模型训练脚本
- `scripts/evaluate.py` - 统一的评估脚本
- `scripts/compare_models.py` - 模型对比脚本

## 注意

这些原始脚本保留用于参考，但建议使用新的统一框架脚本。