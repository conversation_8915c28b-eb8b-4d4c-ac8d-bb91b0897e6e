#!/usr/bin/env python3
"""
框架功能测试脚本
验证各个模块是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        from src.utils import ConfigManager, setup_logger
        from src.models import YOLOModelFactory
        from src.evaluation import UnifiedEvaluator
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("\n🧪 测试配置管理器...")
    
    try:
        from src.utils import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试获取可用配置
        available = config_manager.get_available_configs()
        print(f"✅ 可用配置: {available}")
        
        # 测试加载数据集配置
        if available["datasets"]:
            dataset_config = config_manager.load_dataset_config(available["datasets"][0])
            print(f"✅ 数据集配置加载成功: {dataset_config.name}")
        
        return True
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_model_factory():
    """测试模型工厂"""
    print("\n🧪 测试模型工厂...")
    
    try:
        from src.models import YOLOModelFactory
        
        # 测试获取支持的模型
        supported = YOLOModelFactory.get_supported_models()
        print(f"✅ 支持的模型: {supported}")
        
        # 测试模型名称验证
        valid_names = ["yolov8n", "yolo11s", "yolov13x"]
        for name in valid_names:
            is_valid = YOLOModelFactory.validate_model_name(name)
            if is_valid:
                parsed = YOLOModelFactory.parse_model_name(name)
                print(f"✅ {name}: {parsed}")
            else:
                print(f"❌ {name}: 验证失败")
        
        return True
    except Exception as e:
        print(f"❌ 模型工厂测试失败: {e}")
        return False

def test_evaluator():
    """测试评估器"""
    print("\n🧪 测试评估器...")
    
    try:
        from src.evaluation import UnifiedEvaluator
        
        evaluator = UnifiedEvaluator()
        
        # 测试列出评估实验
        experiments = evaluator.list_evaluations()
        print(f"✅ 现有评估实验: {len(experiments)} 个")
        
        return True
    except Exception as e:
        print(f"❌ 评估器测试失败: {e}")
        return False

def test_logger():
    """测试日志系统"""
    print("\n🧪 测试日志系统...")
    
    try:
        from src.utils import setup_logger
        
        logger = setup_logger("test")
        logger.info("这是一条测试日志")
        print("✅ 日志系统正常工作")
        
        return True
    except Exception as e:
        print(f"❌ 日志系统测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始框架功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置管理器", test_config_manager),
        ("模型工厂", test_model_factory),
        ("评估器", test_evaluator),
        ("日志系统", test_logger)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！框架运行正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    exit(main())