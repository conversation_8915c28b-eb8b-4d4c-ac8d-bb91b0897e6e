"""
YOLOv11模型实现
"""

from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

from .base_model import BaseYOLOModel

logger = logging.getLogger(__name__)

class YOLOv11Model(BaseYOLOModel):
    """YOLOv11模型实现"""
    
    def __init__(self, model_config: Dict[str, Any], weights: Optional[str] = None):
        """初始化YOLOv11模型"""
        super().__init__(model_config, weights)
        self.model_name = f"yolo11{self.variant}.pt"
        
    def load_model(self, weights: Optional[str] = None) -> bool:
        """加载YOLOv11模型"""
        try:
            from ultralytics import YOLO
            
            if weights:
                self.weights = weights
            
            if self.weights and Path(self.weights).exists():
                # 加载自定义权重
                self.model = YOLO(self.weights)
                logger.info(f"加载自定义权重: {self.weights}")
            else:
                # 加载预训练模型
                if self.model_config.get("pretrained", True):
                    self.model = YOLO(self.model_name)
                    logger.info(f"加载预训练模型: {self.model_name}")
                else:
                    # 从配置文件创建模型
                    config_path = f"ultralytics/cfg/models/11/yolo11{self.variant}.yaml"
                    self.model = YOLO(config_path)
                    logger.info(f"从配置创建模型: {config_path}")
            
            # 设置类别数
            if hasattr(self.model.model, 'nc'):
                self.model.model.nc = self.num_classes
            
            return True
            
        except Exception as e:
            logger.error(f"加载YOLOv11模型失败: {e}")
            return False
    
    def train(self, 
              dataset_config: Dict[str, Any],
              training_config: Dict[str, Any], 
              output_dir: str) -> Dict[str, Any]:
        """训练YOLOv11模型"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            # 准备训练参数
            train_args = {
                "data": dataset_config["path"] + "/" + dataset_config.get("config_file", "dataset.yaml"),
                "epochs": training_config.get("epochs", 100),
                "batch": training_config.get("batch_size", 16),
                "imgsz": dataset_config.get("image_size", 640),
                "device": training_config.get("device", "auto"),
                "project": str(Path(output_dir).parent),
                "name": Path(output_dir).name,
                
                # 优化器设置
                "optimizer": training_config.get("optimizer", "AdamW"),
                "lr0": training_config.get("learning_rate", 0.01),
                "lrf": 0.01,
                "momentum": training_config.get("momentum", 0.937),
                "weight_decay": training_config.get("weight_decay", 0.0005),
                "warmup_epochs": training_config.get("warmup_epochs", 3),
                
                # 数据增强
                "hsv_h": training_config.get("hsv_h", 0.015),
                "hsv_s": training_config.get("hsv_s", 0.7),
                "hsv_v": training_config.get("hsv_v", 0.4),
                "degrees": training_config.get("degrees", 0.0),
                "translate": training_config.get("translate", 0.1),
                "scale": training_config.get("scale", 0.5),
                "shear": training_config.get("shear", 0.0),
                "perspective": training_config.get("perspective", 0.0),
                "flipud": training_config.get("flipud", 0.0),
                "fliplr": training_config.get("fliplr", 0.5),
                "mosaic": training_config.get("mosaic", 1.0),
                "mixup": training_config.get("mixup", 0.0),
                "copy_paste": training_config.get("copy_paste", 0.1),
                
                # 训练设置
                "amp": training_config.get("amp", True),
                "cache": training_config.get("cache", False),
                "workers": training_config.get("workers", 4),
                "save_period": training_config.get("save_period", 10),
                "patience": training_config.get("patience", 50),
                "close_mosaic": training_config.get("close_mosaic", 10),
                "seed": training_config.get("seed", 42),
                "deterministic": True,
                "val": True,
                "save": True,
                "plots": True,
                "verbose": True
            }
            
            logger.info("开始训练YOLOv11模型...")
            results = self.model.train(**train_args)
            
            self.is_trained = True
            
            # 提取训练结果
            train_results = {
                "model_name": f"yolo11{self.variant}",
                "final_epoch": train_args["epochs"],
                "best_fitness": float(results.best_fitness) if hasattr(results, 'best_fitness') else None,
                "results_dict": results.results_dict if hasattr(results, 'results_dict') else {},
                "save_dir": str(results.save_dir) if hasattr(results, 'save_dir') else output_dir
            }
            
            logger.info("YOLOv11训练完成")
            return train_results
            
        except Exception as e:
            logger.error(f"YOLOv11训练失败: {e}")
            raise
    
    def evaluate(self,
                dataset_config: Dict[str, Any],
                eval_config: Dict[str, Any]) -> Dict[str, Any]:
        """评估YOLOv11模型"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            # 准备评估参数
            val_args = {
                "data": dataset_config["path"] + "/" + dataset_config.get("config_file", "dataset.yaml"),
                "split": eval_config.get("split", "test"),
                "conf": eval_config.get("conf_threshold", 0.5),
                "iou": eval_config.get("iou_threshold", 0.7),
                "max_det": eval_config.get("max_det", 300),
                "save_json": eval_config.get("save_json", True),
                "save_hybrid": eval_config.get("save_hybrid", False),
                "plots": eval_config.get("plots", True),
                "verbose": True
            }
            
            logger.info("开始评估YOLOv11模型...")
            results = self.model.val(**val_args)
            
            # 提取评估结果
            eval_results = {
                "model_name": f"yolo11{self.variant}",
                "mAP50-95": float(results.box.map),
                "mAP50": float(results.box.map50),
                "mAP75": float(results.box.map75),
                "precision": float(results.box.mp),
                "recall": float(results.box.mr),
                "f1_score": 2 * float(results.box.mp) * float(results.box.mr) / (float(results.box.mp) + float(results.box.mr)) if (float(results.box.mp) + float(results.box.mr)) > 0 else 0,
                "results_dict": results.results_dict if hasattr(results, 'results_dict') else {}
            }
            
            logger.info("YOLOv11评估完成")
            return eval_results
            
        except Exception as e:
            logger.error(f"YOLOv11评估失败: {e}")
            raise
    
    def predict(self, 
                source: str,
                conf_threshold: float = 0.5,
                iou_threshold: float = 0.7,
                save_results: bool = False) -> List[Dict[str, Any]]:
        """YOLOv11预测"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            results = self.model.predict(
                source=source,
                conf=conf_threshold,
                iou=iou_threshold,
                save=save_results,
                verbose=False
            )
            
            # 转换结果格式
            predictions = []
            for result in results:
                pred = {
                    "image_path": result.path,
                    "image_shape": result.orig_shape,
                    "boxes": [],
                    "num_detections": len(result.boxes) if result.boxes is not None else 0
                }
                
                if result.boxes is not None:
                    for box in result.boxes:
                        box_info = {
                            "xyxy": box.xyxy[0].tolist(),
                            "conf": float(box.conf[0]),
                            "cls": int(box.cls[0]),
                            "class_name": self.model.names[int(box.cls[0])] if hasattr(self.model, 'names') else f"class_{int(box.cls[0])}"
                        }
                        pred["boxes"].append(box_info)
                
                predictions.append(pred)
            
            return predictions
            
        except Exception as e:
            logger.error(f"YOLOv11预测失败: {e}")
            raise
    
    def export(self, 
               format: str = "onnx",
               optimize: bool = True,
               half: bool = False) -> str:
        """导出YOLOv11模型"""
        if self.model is None:
            if not self.load_model():
                raise RuntimeError("模型加载失败")
        
        try:
            exported_path = self.model.export(
                format=format,
                optimize=optimize,
                half=half
            )
            
            logger.info(f"YOLOv11模型已导出: {exported_path}")
            return str(exported_path)
            
        except Exception as e:
            logger.error(f"YOLOv11模型导出失败: {e}")
            raise