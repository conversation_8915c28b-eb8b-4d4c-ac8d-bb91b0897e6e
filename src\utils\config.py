"""
配置管理模块
提供统一的配置文件加载和管理功能
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

@dataclass
class DatasetConfig:
    """数据集配置"""
    name: str
    path: str
    config_file: str = "dataset.yaml"
    train_split: str = "train"
    val_split: str = "val"
    test_split: str = "test"
    num_classes: int = 1
    class_names: Dict[int, str] = None
    image_size: int = 640
    description: str = ""

@dataclass
class ModelConfig:
    """模型配置"""
    name: str
    version: str  # v8, v11, v13
    variant: str  # n, s, m, l, x
    pretrained: bool = True
    num_classes: int = 1

@dataclass
class TrainingConfig:
    """训练配置"""
    epochs: int = 100
    batch_size: int = 16
    learning_rate: float = 0.01
    optimizer: str = "AdamW"
    scheduler: str = "cosine"
    warmup_epochs: int = 3
    weight_decay: float = 0.0005
    momentum: float = 0.937
    
    # 数据增强
    hsv_h: float = 0.015
    hsv_s: float = 0.7
    hsv_v: float = 0.4
    degrees: float = 0.0
    translate: float = 0.1
    scale: float = 0.5
    shear: float = 0.0
    perspective: float = 0.0
    flipud: float = 0.0
    fliplr: float = 0.5
    mosaic: float = 1.0
    mixup: float = 0.0
    copy_paste: float = 0.1
    
    # 训练设置
    amp: bool = True
    cache: bool = False
    workers: int = 4
    save_period: int = 10
    patience: int = 50
    close_mosaic: int = 10
    seed: int = 42

@dataclass
class EvaluationConfig:
    """评估配置"""
    conf_threshold: float = 0.5
    iou_threshold: float = 0.7
    max_det: int = 300
    save_json: bool = True
    save_hybrid: bool = False
    plots: bool = True

@dataclass
class ExperimentConfig:
    """实验配置"""
    name: str
    description: str = ""
    dataset: DatasetConfig = None
    model: ModelConfig = None
    training: TrainingConfig = None
    evaluation: EvaluationConfig = None
    output_dir: str = "experiments"
    seed: int = 42
    device: str = "auto"

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_dir: str = "configs"):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录
        """
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        
    def load_yaml(self, config_path: Union[str, Path]) -> Dict[str, Any]:
        """
        加载YAML配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        config_path = Path(config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"成功加载配置文件: {config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败 {config_path}: {e}")
            raise
    
    def save_yaml(self, config: Dict[str, Any], config_path: Union[str, Path]):
        """
        保存配置到YAML文件
        
        Args:
            config: 配置字典
            config_path: 保存路径
        """
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
            logger.info(f"配置文件已保存: {config_path}")
        except Exception as e:
            logger.error(f"保存配置文件失败 {config_path}: {e}")
            raise
    
    def load_dataset_config(self, dataset_name: str) -> DatasetConfig:
        """
        加载数据集配置
        
        Args:
            dataset_name: 数据集名称
            
        Returns:
            数据集配置对象
        """
        config_path = self.config_dir / "datasets" / f"{dataset_name}.yaml"
        config_dict = self.load_yaml(config_path)
        return DatasetConfig(**config_dict)
    
    def load_model_config(self, model_version: str, variant: str = "n") -> ModelConfig:
        """
        加载模型配置
        
        Args:
            model_version: 模型版本 (v8, v11, v13)
            variant: 模型变体 (n, s, m, l, x)
            
        Returns:
            模型配置对象
        """
        config_path = self.config_dir / "models" / f"yolo{model_version}" / f"yolo{model_version}{variant}.yaml"
        if config_path.exists():
            config_dict = self.load_yaml(config_path)
        else:
            # 使用默认配置
            config_dict = {
                "name": f"yolo{model_version}{variant}",
                "version": model_version,
                "variant": variant,
                "pretrained": True,
                "num_classes": 1
            }
        return ModelConfig(**config_dict)
    
    def load_training_config(self, config_name: str = "base_config") -> TrainingConfig:
        """
        加载训练配置
        
        Args:
            config_name: 配置名称
            
        Returns:
            训练配置对象
        """
        config_path = self.config_dir / "training" / f"{config_name}.yaml"
        if config_path.exists():
            config_dict = self.load_yaml(config_path)
            return TrainingConfig(**config_dict)
        else:
            logger.warning(f"训练配置文件不存在: {config_path}，使用默认配置")
            return TrainingConfig()
    
    def load_evaluation_config(self, config_name: str = "base_config") -> EvaluationConfig:
        """
        加载评估配置
        
        Args:
            config_name: 配置名称
            
        Returns:
            评估配置对象
        """
        config_path = self.config_dir / "evaluation" / f"{config_name}.yaml"
        if config_path.exists():
            config_dict = self.load_yaml(config_path)
            return EvaluationConfig(**config_dict)
        else:
            logger.warning(f"评估配置文件不存在: {config_path}，使用默认配置")
            return EvaluationConfig()
    
    def create_experiment_config(self, 
                               experiment_name: str,
                               dataset_name: str,
                               model_version: str,
                               model_variant: str = "n",
                               training_config: str = "base_config",
                               description: str = "") -> ExperimentConfig:
        """
        创建实验配置
        
        Args:
            experiment_name: 实验名称
            dataset_name: 数据集名称
            model_version: 模型版本
            model_variant: 模型变体
            training_config: 训练配置名称
            description: 实验描述
            
        Returns:
            实验配置对象
        """
        dataset_config = self.load_dataset_config(dataset_name)
        model_config = self.load_model_config(model_version, model_variant)
        training_config = self.load_training_config(training_config)
        evaluation_config = self.load_evaluation_config()
        
        return ExperimentConfig(
            name=experiment_name,
            description=description,
            dataset=dataset_config,
            model=model_config,
            training=training_config,
            evaluation=evaluation_config
        )
    
    def save_experiment_config(self, config: ExperimentConfig, save_path: Optional[str] = None):
        """
        保存实验配置
        
        Args:
            config: 实验配置对象
            save_path: 保存路径，如果为None则自动生成
        """
        if save_path is None:
            save_path = self.config_dir / "experiments" / f"{config.name}.yaml"
        
        config_dict = asdict(config)
        self.save_yaml(config_dict, save_path)
    
    def get_available_configs(self) -> Dict[str, list]:
        """
        获取所有可用的配置文件
        
        Returns:
            配置文件列表字典
        """
        available = {
            "datasets": [],
            "models": {"v8": [], "v11": [], "v13": []},
            "training": [],
            "evaluation": []
        }
        
        # 数据集配置
        dataset_dir = self.config_dir / "datasets"
        if dataset_dir.exists():
            available["datasets"] = [f.stem for f in dataset_dir.glob("*.yaml")]
        
        # 模型配置
        for version in ["v8", "v11", "v13"]:
            model_dir = self.config_dir / "models" / f"yolo{version}"
            if model_dir.exists():
                available["models"][version] = [f.stem for f in model_dir.glob("*.yaml")]
        
        # 训练配置
        training_dir = self.config_dir / "training"
        if training_dir.exists():
            available["training"] = [f.stem for f in training_dir.glob("*.yaml")]
        
        # 评估配置
        eval_dir = self.config_dir / "evaluation"
        if eval_dir.exists():
            available["evaluation"] = [f.stem for f in eval_dir.glob("*.yaml")]
        
        return available