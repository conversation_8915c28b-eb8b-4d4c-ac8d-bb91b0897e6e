"""
YOLO多模型卫星目标检测基线框架安装配置
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
if readme_file.exists():
    with open(readme_file, "r", encoding="utf-8") as f:
        long_description = f.read()
else:
    long_description = "YOLO多模型卫星目标检测基线框架"

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
if requirements_file.exists():
    with open(requirements_file, "r", encoding="utf-8") as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]
else:
    requirements = [
        "torch>=2.0.0",
        "torchvision>=0.15.0",
        "ultralytics>=8.0.0",
        "opencv-python>=4.6.0",
        "matplotlib>=3.3.0",
        "pyyaml>=5.3.1",
        "numpy>=1.23.0",
        "pandas>=1.1.4",
        "tqdm>=4.64.0",
        "pillow>=7.1.2"
    ]

setup(
    name="yolo-satellite-baseline",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="多YOLO模型卫星目标检测训练与评估基线框架",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/yolo-satellite-baseline",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Image Recognition",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.0",
            "flake8>=3.8",
            "black>=21.0",
            "isort>=5.0"
        ]
    },
    entry_points={
        "console_scripts": [
            "yolo-train=scripts.train:main",
            "yolo-eval=scripts.evaluate:main",
            "yolo-data=scripts.prepare_data:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["configs/**/*.yaml", "docs/**/*.md"],
    },
)