"""
日志系统模块
提供统一的日志记录功能
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from datetime import datetime

def setup_logger(name: str = "yolo_baseline", 
                level: int = logging.INFO,
                log_file: Optional[str] = None,
                format_str: Optional[str] = None) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志器名称
        level: 日志级别
        log_file: 日志文件路径，如果为None则只输出到控制台
        format_str: 日志格式字符串
        
    Returns:
        配置好的日志器
    """
    if format_str is None:
        format_str = "[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s"
    
    # 创建日志器
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 清除已有的处理器
    logger.handlers.clear()
    
    # 创建格式器
    formatter = logging.Formatter(format_str, datefmt="%Y-%m-%d %H:%M:%S")
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_experiment_logger(experiment_name: str, 
                         output_dir: str = "experiments") -> logging.Logger:
    """
    获取实验专用日志器
    
    Args:
        experiment_name: 实验名称
        output_dir: 输出目录
        
    Returns:
        实验日志器
    """
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = Path(output_dir) / "logs" / f"{experiment_name}_{timestamp}.log"
    
    return setup_logger(
        name=f"experiment_{experiment_name}",
        log_file=str(log_file)
    )