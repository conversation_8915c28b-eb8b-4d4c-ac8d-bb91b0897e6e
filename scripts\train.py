#!/usr/bin/env python3
"""
统一的多YOLO模型训练脚本

支持YOLOv8、YOLOv11、YOLOv13的训练
"""

import argparse
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils import ConfigManager, setup_logger, get_device, set_seed, create_experiment_dir
from src.models import YOLOModelFactory
import logging

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="多YOLO模型训练脚本")
    
    # 基本参数
    parser.add_argument("--model", type=str, required=True,
                       help="模型名称 (例如: yolov8n, yolo11s, yolov13x)")
    parser.add_argument("--dataset", type=str, default="aqua",
                       help="数据集名称")
    parser.add_argument("--config", type=str, default="base_config",
                       help="训练配置名称")
    parser.add_argument("--experiment", type=str, required=True,
                       help="实验名称")
    
    # 训练参数覆盖
    parser.add_argument("--epochs", type=int, default=None,
                       help="训练轮数")
    parser.add_argument("--batch-size", type=int, default=None,
                       help="批次大小")
    parser.add_argument("--lr", type=float, default=None,
                       help="学习率")
    parser.add_argument("--device", type=str, default="auto",
                       help="设备 (auto, cpu, gpu, 0, 1, etc.)")
    
    # 其他选项
    parser.add_argument("--pretrained", action="store_true", default=True,
                       help="使用预训练权重")
    parser.add_argument("--weights", type=str, default=None,
                       help="自定义权重文件路径")
    parser.add_argument("--resume", type=str, default=None,
                       help="恢复训练的检查点路径")
    parser.add_argument("--seed", type=int, default=42,
                       help="随机种子")
    parser.add_argument("--verbose", action="store_true",
                       help="详细输出")
    
    return parser.parse_args()

def main():
    """主函数"""
    args = parse_args()
    
    # 设置日志
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logger = setup_logger("train", level=log_level)
    
    logger.info("🚀 启动多YOLO模型训练系统")
    logger.info(f"模型: {args.model}")
    logger.info(f"数据集: {args.dataset}")
    logger.info(f"实验: {args.experiment}")
    
    try:
        # 设置随机种子
        set_seed(args.seed)
        
        # 获取设备
        device = get_device(args.device)
        
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 解析模型名称
        try:
            model_info = YOLOModelFactory.parse_model_name(args.model)
            version = model_info["version"]
            variant = model_info["variant"]
        except ValueError as e:
            logger.error(f"无效的模型名称: {e}")
            return 1
        
        # 加载数据集配置
        try:
            dataset_config = config_manager.load_dataset_config(args.dataset)
            logger.info(f"✅ 加载数据集配置: {args.dataset}")
        except Exception as e:
            logger.error(f"❌ 加载数据集配置失败: {e}")
            return 1
        
        # 加载训练配置
        try:
            # 尝试加载特定版本的配置，否则使用基础配置
            config_name = f"yolo{version}_config" if args.config == "base_config" else args.config
            training_config = config_manager.load_training_config(config_name)
            logger.info(f"✅ 加载训练配置: {config_name}")
        except Exception as e:
            logger.warning(f"⚠️  加载特定训练配置失败，使用基础配置: {e}")
            training_config = config_manager.load_training_config("base_config")
        
        # 应用命令行参数覆盖
        if args.epochs is not None:
            training_config.epochs = args.epochs
        if args.batch_size is not None:
            training_config.batch_size = args.batch_size
        if args.lr is not None:
            training_config.learning_rate = args.lr
        
        # 创建实验目录
        exp_dir = create_experiment_dir(args.experiment)
        logger.info(f"📁 实验目录: {exp_dir}")
        
        # 创建模型
        try:
            model = YOLOModelFactory.create_model(
                version=version,
                variant=variant,
                num_classes=dataset_config.num_classes,
                pretrained=args.pretrained,
                weights=args.weights
            )
            logger.info(f"✅ 创建模型: {model}")
        except Exception as e:
            logger.error(f"❌ 创建模型失败: {e}")
            return 1
        
        # 加载模型
        try:
            success = model.load_model()
            if not success:
                logger.error("❌ 模型加载失败")
                return 1
            logger.info("✅ 模型加载成功")
        except Exception as e:
            logger.error(f"❌ 模型加载异常: {e}")
            return 1
        
        # 准备训练配置
        train_config_dict = {
            "epochs": training_config.epochs,
            "batch_size": training_config.batch_size,
            "learning_rate": training_config.learning_rate,
            "optimizer": training_config.optimizer,
            "momentum": training_config.momentum,
            "weight_decay": training_config.weight_decay,
            "warmup_epochs": training_config.warmup_epochs,
            "device": device,
            "seed": args.seed,
            
            # 数据增强
            "hsv_h": training_config.hsv_h,
            "hsv_s": training_config.hsv_s,
            "hsv_v": training_config.hsv_v,
            "degrees": training_config.degrees,
            "translate": training_config.translate,
            "scale": training_config.scale,
            "shear": training_config.shear,
            "perspective": training_config.perspective,
            "flipud": training_config.flipud,
            "fliplr": training_config.fliplr,
            "mosaic": training_config.mosaic,
            "mixup": training_config.mixup,
            "copy_paste": training_config.copy_paste,
            
            # 训练设置
            "amp": training_config.amp,
            "cache": training_config.cache,
            "workers": training_config.workers,
            "save_period": training_config.save_period,
            "patience": training_config.patience,
            "close_mosaic": training_config.close_mosaic
        }
        
        dataset_config_dict = {
            "name": dataset_config.name,
            "path": dataset_config.path,
            "config_file": dataset_config.config_file,
            "image_size": dataset_config.image_size,
            "num_classes": dataset_config.num_classes
        }
        
        # 保存实验配置
        experiment_config = config_manager.create_experiment_config(
            experiment_name=args.experiment,
            dataset_name=args.dataset,
            model_version=version,
            model_variant=variant,
            training_config=args.config,
            description=f"训练{args.model}模型在{args.dataset}数据集上"
        )
        
        config_save_path = exp_dir / "configs" / "experiment_config.yaml"
        config_manager.save_experiment_config(experiment_config, config_save_path)
        
        # 开始训练
        logger.info("🏋️  开始训练...")
        train_results = model.train(
            dataset_config=dataset_config_dict,
            training_config=train_config_dict,
            output_dir=str(exp_dir)
        )
        
        # 保存训练结果
        from src.utils.helpers import save_checkpoint
        results_path = exp_dir / "results" / "train_results.json"
        save_checkpoint(train_results, str(results_path), format="json")
        
        logger.info("🎉 训练完成！")
        logger.info(f"📊 训练结果:")
        if "results_dict" in train_results and train_results["results_dict"]:
            results = train_results["results_dict"]
            for key, value in results.items():
                if isinstance(value, (int, float)):
                    logger.info(f"   {key}: {value:.4f}")
        
        logger.info(f"📁 结果保存在: {exp_dir}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("⏹️  训练被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 训练过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit(main())